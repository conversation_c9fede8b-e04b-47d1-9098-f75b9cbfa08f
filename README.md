# Auth Action Module

`ActionController::AuthAction` is a Rails concern that provides flexible authentication handling for your controllers. It supports token-based authentication with customizable validation logic.

## Basic Usage

Include the concern in your controller:

```ruby
class ApiController < ApplicationController
  include ActionController::AuthAction
end
```

### Simple Authentication

```ruby
class UsersController < ApiController
  auth_action :user
  
  def index
    # current_user is available here
  end
end

class MembersController < ApiController
  auth_action :member
  
  def index
    # current_member is available here
  end
end
```

### Skip Error Handling

If you want to handle authentication errors yourself:

```ruby
class PublicController < ApiController
  auth_action :user, skip_error: true
  
  def index
    # Authentication will not raise errors
  end
end
```

## Advanced Usage

### Custom Validation

You can add custom validation logic using a Proc, Symbol, or String:

```ruby
class ApiController < ApplicationController
  # Using Proc
  auth_action :user, validator: -> {
    raise Error::AuthError unless current_user.verified?
  }

  # Using Symbol
  auth_action :member, validator: :validate_member_status

  # Using String
  auth_action :user, validator: "check_permissions"

  private

  def validate_member_status
    raise Error::AuthError unless current_member.active?
  end

  def check_permissions
    raise Error::AuthError unless current_user.has_permission?(:api_access)
  end
end
```

### Controller Options

You can pass standard Rails controller options:

```ruby
class ApiController < ApplicationController
  auth_action :user, only: [:index, :show]
  auth_action :member, except: [:public_action]
end
```

## Available Methods

After setting up `auth_action`, these methods become available:

- `current_auth`: The authenticated object (user or member)
- `current_user`: The authenticated user
- `current_member`: The authenticated member (if applicable)
- `current_app`: The current application
- `current_[type]`: Dynamic method based on the auth_action type

## Authentication Flow

1. Extracts token from `HTTP_AUTHORIZATION` header
2. Authenticates token and loads user information
3. Loads appropriate auth object (user/member)
4. Runs any custom validation if specified
5. Makes auth object available via current_* methods

## Error Handling

By default, authentication failures raise `Error::AuthError`. Use `skip_error: true` to handle errors manually.

Common error scenarios:
- Missing or invalid token
- Blocked user account
- Custom validation failures

## Headers

The module expects these headers:
- `HTTP_AUTHORIZATION`: Authentication token (format: "Token xyz")
- `HTTP_APPCODE`: Application code (optional)
- `HTTP_MEMBERID`: Member ID (optional)


```ruby
# base_inject_route 使用示例
module SomeFeature
  module Routes
    include RailsCom::ActionRoute::BaseInjectRoute

    FEATURE_ACTIONS = [:index, :show, :create, :update, :destroy]

    def feature_routes(*actions)
      options = actions.last.is_a?(Hash) ? actions.pop : {}
      setup_included_actions(actions, FEATURE_ACTIONS, options)

      routes_config = [
        { method: :get, path: :features, action: :index, options: { on: :collection } },
        { method: :get, path: 'features/:id', action: :show },
        { method: :post, path: :features, action: :create, options: { on: :collection } },
        { method: :put, path: 'features/:id', action: :update },
        { method: :delete, path: 'features/:id', action: :destroy }
      ]

      define_member_routes(routes_config, prefix: 'api', **options)

      # Inject controller methods
      controller_modules = {
        SomeFeature::Controller => -> { true }
      }

      inject_controller_methods!(controller_modules)
    end
  end
end

# 使用方式示例：
# 在 routes.rb 中：

# 使用 only 选项
feature_routes only: [:index, :show]

# 使用 except 选项
feature_routes except: [:destroy]

# 使用默认全部动作
feature_routes

# 混合使用
feature_routes :index, :show, except: [:show]  # 最终只会包含 index
```
