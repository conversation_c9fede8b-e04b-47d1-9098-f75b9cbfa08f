Rails.application.routes.draw do
  namespace :com do
    namespace :admin do
      resources :apps, except: [:destroy] do
        resources :themes
      end
      resources :mods
      resources :model_defines, shallow: true do
        resources :model_settings
      end
      resources :model_confs
      resources :api_settings
      resources :roles
      resources :search_items
      resources :private_policies
      resources :version_histories
      resources :page_settings
      resources :component_settings
      resources :data_forms
      resources :paper_trail_versions
    end

    namespace :user do
      resources :mods, only: [:index, :show]
      resources :themes, only: [:index, :show]
      resources :search_items, only: [:index, :show]
      resources :record_storages, only: [:create] do
        post :fetch, on: :collection
        post :clear, on: :collection
      end
      resources :model_settings, only: [:index, :show]
      resources :api_settings, only: [:index, :show]
      resources :page_settings, only: [:index, :show]
      resources :model_confs, only: [:index, :show]
      resources :async_tasks, only: [:index, :show, :destroy]
    end

    resources :endpoints, only: [] do
      post :fetch_data_by_keys, on: :collection
    end
  end

  namespace :forms do
    namespace :admin do
      resources :templates do
        post :clone, on: :member
      end
    end

    resources :resource_infos, only: [] do
      post :find_by_ids, on: :collection
      post :find_by_file, on: :collection
      post :upload_excel, on: :collection
      post :import_headers, on: :collection
      post :export_import_template, on: :collection
      post :excel, on: :collection
      post :enable_actions, on: :collection
      post :member_enable_actions, on: :collection
      post :collection_enable_actions, on: :collection
    end

    namespace :user do
      resources :templates, only: [:index, :show]

      namespace :setable do
        scope '/:model_singular_name/:flag' do
          resource :template, only: [:show]
        end
      end
    end
  end

  namespace :storage do
    resources :direct_uploads, only: [:create]
    # POST multipart 开始任务
    # POST multipart/list 查看任务
    # DELETE multipart 删除任务
    # POST multipart/part 获取单个分片内容
    # POST multipart/complete 完成整个任务
    resource :multipart, only: [:create, :destroy] do
      post :list, on: :member
      post :complete, on: :member
      post :part, on: :member
    end
  end

  resources :apps, only: [:index, :show] do
    resource :private_policy, only: [:show]
    resources :com_private_policies, only: [:index, :show]
    resources :version_histories, only: [:index, :show]
    resources :tanents, only: [:index, :show]
  end
end
