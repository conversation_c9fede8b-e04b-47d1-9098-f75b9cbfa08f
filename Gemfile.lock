GIT
  remote: ******************:open-source/rspec-rails-swagger.git
  revision: c1b2bb18d1fc3358c0640355750230b1bbc20476
  specs:
    rspec-rails-swagger (0.1.4)
      rails (>= 3.1)
      rspec-rails

GIT
  remote: ******************:open-source/simple_controller.git
  revision: d00f83913620d70cced0cfd612ecb9abba2d1ca5
  specs:
    simple_controller (1.1.0)
      inherited_resources
      pundit
      ransack
      responders
      ta_ransack_mongo
      will_paginate

PATH
  remote: .
  specs:
    rails_com (0.1.0)
      aasm
      active_record_extended
      acts_as_list
      ancestry
      attr_json
      aws-sdk-s3
      closure_tree
      deep_cloneable
      execjs
      groupdate
      jbuilder
      matrix
      mime-types
      nilify_blanks
      ohm
      paper_trail
      paranoia
      pundit
      rack-cors
      rails
      ransack
      ransack-enum
      redis
      redis-namespace
      redis-objects
      responders
      rolify
      ruby-pinyin
      strip_attributes
      ta_by_star
      ta_deep_pluck
      ta_default_value_for
      ta_has_event
      typhoeus
      uppy-s3_multipart
      uuidtools
      zip_tricks

GEM
  remote: https://gems.ruby-china.com/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_record_extended (3.2.1)
      activerecord (>= 5.2, < 7.2.0)
      pg (< 3.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    acts_as_list (1.1.0)
      activerecord (>= 4.2)
    ancestry (4.3.3)
      activerecord (>= 5.2.6)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    attr_json (2.3.0)
      activerecord (>= 6.0.0, < 7.2)
    awesome_print (1.9.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.895.0)
    aws-sdk-core (3.191.3)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.77.0)
      aws-sdk-core (~> 3, >= 3.191.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.143.0)
      aws-sdk-core (~> 3, >= 3.191.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.8)
    aws-sigv4 (1.8.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.6)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    builder (3.2.4)
    byebug (11.1.3)
    closure_tree (7.4.0)
      activerecord (>= 4.2.10)
      with_advisory_lock (>= 4.0.0)
    coderay (1.1.3)
    concurrent-ruby (1.2.3)
    connection_pool (2.4.1)
    content_disposition (1.0.0)
    crass (1.0.6)
    date (3.3.4)
    debug_inspector (1.2.0)
    deep_cloneable (3.2.0)
      activerecord (>= 3.1.0, < 8)
    diff-lcs (1.5.1)
    docile (1.4.0)
    dotenv (3.1.0)
    dotenv-rails (3.1.0)
      dotenv (= 3.1.0)
      railties (>= 6.1)
    drb (2.2.1)
    erubi (1.12.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    execjs (2.9.1)
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    ffi (1.16.3)
    globalid (1.2.1)
      activesupport (>= 6.1)
    groupdate (6.4.0)
      activesupport (>= 6.1)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hirb (0.7.3)
    hiredis (0.6.3)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.7.2)
    irb (1.11.2)
      rdoc
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.0.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.0206)
    mini_mime (1.1.5)
    mini_portile2 (2.8.5)
    minitest (5.22.2)
    mutex_m (0.2.0)
    nest (3.2.0)
      redic
    net-imap (0.4.10)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.4.0.1)
      net-protocol
    nilify_blanks (1.4.0)
      activerecord (>= 4.0.0)
      activesupport (>= 4.0.0)
    nio4r (2.7.0)
    nokogiri (1.16.2)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    ohm (3.1.1)
      nest (~> 3)
      redic (~> 1.5.0)
      stal
    paper_trail (15.1.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    paranoia (2.6.3)
      activerecord (>= 5.1, < 7.2)
    pg (1.5.6)
    pluck_all (2.3.4)
      activesupport (>= 3.0.0)
      rails_compatibility (>= 0.0.10)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    pry-doc (1.5.0)
      pry (~> 0.11)
      yard (~> 0.9.11)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    psych (5.1.2)
      stringio
    pundit (2.3.1)
      activesupport (>= 3.0.0)
    racc (1.7.3)
    rack (3.0.9.1)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rails_compatibility (0.0.10)
      activerecord (>= 3)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.1.0)
    ransack (3.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    ransack-enum (1.0.0)
    rdoc (6.6.2)
      psych (>= 4.0.0)
    redic (1.5.3)
      hiredis
    redis (5.1.0)
      redis-client (>= 0.17.0)
    redis-client (0.20.0)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-objects (1.7.0)
      redis
    reline (0.4.3)
      io-console (~> 0.5)
    request_store (1.6.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rmmseg-cpp-new (0.3.1)
    roda (3.77.0)
      rack
    rolify (6.0.1)
    rouge (4.2.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.12)
      rspec-expectations (~> 3.12)
      rspec-mocks (~> 3.12)
      rspec-support (~> 3.12)
    rspec-support (3.13.1)
    ruby-pinyin (0.5.0)
      rmmseg-cpp-new (~> 0.3.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    shoulda-matchers (5.3.0)
      activesupport (>= 5.2.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sqlite3 (1.7.2)
      mini_portile2 (~> 2.8.0)
    stal (0.3.0)
      redic (~> 1.5)
    stringio (3.1.0)
    strip_attributes (1.13.0)
      activemodel (>= 3.0, < 8.0)
    ta_by_star (4.1.0)
      activesupport (>= 3.2.0)
    ta_deep_pluck (1.3.0)
      activerecord (>= 3)
      pluck_all (>= 2.3.2)
      rails_compatibility (>= 0.0.4)
    ta_default_value_for (3.4.0)
      activerecord (>= 3.2.0, < 8.0)
    ta_has_event (1.0.1)
      activerecord (>= 4.0)
      verbs
    ta_ransack_mongo (1.0.3)
    thor (1.3.1)
    tilt (2.3.0)
    timeout (0.4.1)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uppy-s3_multipart (1.2.1)
      aws-sdk-s3 (~> 1.0)
      content_disposition (~> 1.0)
      roda (>= 2.27, < 4)
    uuidtools (2.2.0)
    verbs (3.1.0)
      activesupport (>= 2.3.4)
      i18n
    webrick (1.8.1)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (4.0.0)
    with_advisory_lock (5.1.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    yard (0.9.36)
    zeitwerk (2.6.13)
    zip_tricks (5.6.0)

PLATFORMS
  ruby

DEPENDENCIES
  annotate
  awesome_print
  better_errors
  binding_of_caller
  dotenv-rails
  factory_bot_rails
  hirb
  pg
  pry-byebug
  pry-doc
  pry-rails
  rails_com!
  rspec-rails
  rspec-rails-swagger!
  sassc-rails
  shoulda-matchers
  simple_controller!
  simplecov
  sqlite3

BUNDLED WITH
   2.5.23
