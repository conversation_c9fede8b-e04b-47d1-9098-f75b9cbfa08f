source 'https://gems.ruby-china.com'

gem 'dotenv-rails', require: 'dotenv/load'

git_source(:github) { |repo| "https://github.com/#{repo}.git" }
git_source(:tallty) { |repo| "******************:#{repo}.git" }

# Specify your gem's dependencies in rails_com.gemspec.
gemspec

group :development do
  gem 'sqlite3'
end

gem 'pg'
gem 'simple_controller', tallty: 'open-source/simple_controller'
group :development, :test do
  gem 'factory_bot_rails'
  gem 'pry-byebug'
  gem 'rspec-rails'
  gem 'rspec-rails-swagger', tallty: 'open-source/rspec-rails-swagger'
  gem 'shoulda-matchers'
end
gem 'simplecov', require: false, group: :test
