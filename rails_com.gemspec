require_relative 'lib/rails_com/version'

Gem::Specification.new do |spec|
  spec.name        = 'rails_com'
  spec.version     = RailsCom::VERSION
  spec.authors     = ['liyijie']
  spec.email       = ['<EMAIL>']
  spec.homepage    = 'http://www.tallty.com'
  spec.summary     = 'Summary of RailsCom.'
  spec.description = 'Description of RailsCom.'
  spec.license     = 'MIT'

  # Prevent pushing this gem to RubyGems.org. To allow pushes either set the 'allowed_push_host'
  # to allow pushing to a single host or delete this section to allow pushing to any host.
  spec.metadata['allowed_push_host'] = "Set to 'http://mygemserver.com'"

  spec.metadata['homepage_uri'] = spec.homepage
  spec.metadata['source_code_uri'] = 'http://www.tallty.com'
  spec.metadata['changelog_uri'] = 'http://www.tallty.com'

  spec.files = Dir['{app,config,db,lib}/**/*', 'MIT-LICENSE', 'Rakefile', 'README.md']

  spec.add_dependency 'rails'

  spec.add_dependency 'aasm'
  spec.add_dependency 'active_record_extended'
  spec.add_dependency 'acts_as_list'
  spec.add_dependency 'ancestry'
  spec.add_dependency 'attr_json'
  spec.add_dependency 'aws-sdk-s3'
  spec.add_dependency 'closure_tree'
  spec.add_dependency 'deep_cloneable'
  spec.add_dependency 'ta_default_value_for'
  spec.add_dependency 'execjs'
  spec.add_dependency 'groupdate'
  spec.add_dependency 'jbuilder'
  spec.add_dependency 'mime-types'
  spec.add_dependency 'nilify_blanks'
  spec.add_dependency 'ohm'
  spec.add_dependency 'paper_trail'
  spec.add_dependency 'paranoia'
  spec.add_dependency 'pundit'
  spec.add_dependency 'rack-cors'
  spec.add_dependency 'ransack'
  spec.add_dependency 'ransack-enum'
  spec.add_dependency 'redis'
  spec.add_dependency 'redis-namespace'
  spec.add_dependency 'redis-objects'
  spec.add_dependency 'responders'
  spec.add_dependency 'rolify'
  spec.add_dependency 'ruby-pinyin'
  spec.add_dependency 'strip_attributes'
  spec.add_dependency 'ta_by_star'
  spec.add_dependency 'ta_deep_pluck'
  spec.add_dependency 'typhoeus'
  spec.add_dependency 'uppy-s3_multipart'
  spec.add_dependency 'uuidtools'
  spec.add_dependency 'zip_tricks'
  spec.add_dependency 'matrix'
  spec.add_dependency 'ta_has_event'


  spec.add_development_dependency 'annotate'
  spec.add_development_dependency 'awesome_print'
  spec.add_development_dependency 'better_errors'
  spec.add_development_dependency 'binding_of_caller'
  spec.add_development_dependency 'hirb'
  spec.add_development_dependency 'pry-doc'
  spec.add_development_dependency 'pry-rails'
  spec.add_development_dependency 'sassc-rails'
end
