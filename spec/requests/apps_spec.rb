require 'swagger_helper'

RSpec.describe '/apps', type: :request, capture_examples: true, tags: [""] do
  before :each do
  end

  path '/apps/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show app') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @apps.first.code }
        it {
          body = JSON.parse(response.body)
          app = @apps.first
        }
      end
    end
  end
end
