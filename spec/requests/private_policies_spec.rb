require 'swagger_helper'

RSpec.describe '/private_policies', type: :request, capture_examples: true, tags: [""] do
  before :each do
  end

  path '/apps/{app_id}/private_policy' do
    parameter 'app_id', in: :path, type: :string

    get(summary: 'show private_policy') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:app_id) { @app.id }
        it {
          body = JSON.parse(response.body)
          private_policy = @com_private_policies.order(position: :desc).first
          expect(body['app_id']).to eq private_policy.app_id
          expect(body['name']).to eq private_policy.name
          expect(body['key']).to eq private_policy.key
          expect(body['content']).to eq private_policy.content
          expect(body['position']).to eq private_policy.position
        }
      end
    end
  end
end
