require 'swagger_helper'

RSpec.describe '/version_histories', type: :request, capture_examples: true, tags: [""] do

  before :each do
  end

  path '/apps/{app_id}/version_histories' do
    parameter 'app_id', in: :path, type: :string

    get(summary: 'list version_histories') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:app_id) { @app.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @com_version_history_count
        }
      end
    end
  end

  path '/apps/{app_id}/version_histories/{id}' do
    parameter 'app_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show version_history') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:app_id) { @app.id }
        let(:id) { @com_version_histories.first.id }
        it {
          body = JSON.parse(response.body)
          version_history = @com_version_histories.first
          expect(body['app_id']).to eq version_history.app_id
          expect(body['creator_id']).to eq version_history.creator_id
          expect(body['model_flag']).to eq version_history.model_flag
          expect(body['model_payload']).to eq version_history.model_payload
          expect(body['model_payload_summary']).to eq version_history.model_payload_summary
          expect(body['name']).to eq version_history.name
          expect(body['version']).to eq version_history.version
          expect(body['content']).to eq version_history.content
          expect(body['position']).to eq version_history.position
        }
      end
    end
  end
end
