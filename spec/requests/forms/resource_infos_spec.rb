require 'swagger_helper'

RSpec.describe 'forms/resource_infos', type: :request, capture_examples: true, tags: ["forms"] do
  resource_info_ref = {
    type: :object, properties: {
      resource_info: {
        type: :object, properties: {
          path: { type: :string, description: '对应的API路径' },
          resource_id: { type: :string, description: '对应resource的id' },
          ids: { type: :integer, array: true, description: '对应的id列表' },
          attrs: { type: :string, array: true, description: '对应需要查询的属性列表' },
        }
      }
    }
  }

  before :each do
  end

  path '/forms/resource_infos/enable_actions' do
    post(summary: 'enable_actions resource_info') do
      produces 'application/json'
      consumes 'application/json'
      parameter :resource_info, in: :body, schema: resource_info_ref
      response(201, description: 'successful') do
        before :each do
          @user.add_role :com_admin
        end
        let(:resource_info) do
          { resource_info: {
            path: 'com/admin/apps',
          } }
        end
        it {
          body = JSON.parse(response.body)
        }
      end
    end
  end

  path '/forms/resource_infos/collection_enable_actions' do
    post(summary: 'collection_enable_actions resource_info') do
      produces 'application/json'
      consumes 'application/json'
      parameter :resource_info, in: :body, schema: resource_info_ref
      response(201, description: 'successful') do
        before :each do
          @user.add_role :com_admin
        end
        let(:resource_info) do
          { resource_info: {
            path: 'com/admin/apps',
          } }
        end
        it {
          body = JSON.parse(response.body)
        }
      end
    end
  end

  path '/forms/resource_infos/member_enable_actions' do
    post(summary: 'member_enable_actions resource_info') do
      produces 'application/json'
      consumes 'application/json'
      parameter :resource_info, in: :body, schema: resource_info_ref
      response(201, description: 'successful') do
        before :each do
          @user.add_role :com_admin
        end
        let(:resource_info) do
          { resource_info: {
            path: 'com/admin/apps',
            resource_id: @app.id,
          } }
        end
        it {
          body = JSON.parse(response.body)
        }
      end
    end
  end

  path '/forms/resource_infos/find_by_ids' do

    post(summary: 'find_by_ids resource_info') do
      produces 'application/json'
      consumes 'application/json'
      parameter :resource_info, in: :body, schema: resource_info_ref
      response(200, description: 'successful') do
        let(:resource_info) do
          { resource_info: {
            path: 'com/admin/apps',
            ids: App.pluck(:id),
            attrs: ['name', 'code']
          } }
        end
        it {
          body = JSON.parse(response.body)
        }
      end
    end
  end
end
