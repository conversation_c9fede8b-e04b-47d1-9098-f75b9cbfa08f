require 'swagger_helper'

RSpec.describe 'forms/user/setable/templates', type: :request, capture_examples: true, tags: ['forms user setable'] do
  before :each do
  end

  path '/forms/user/setable/{model_singular_name}/{flag}/template' do
    parameter 'model_singular_name', in: :path, type: :string
    parameter 'flag', in: :path, type: :string

    get(summary: 'show template') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:model_singular_name) { 'user' }
        let(:flag) { 'model' }
        it {
          body = JSON.parse(response.body)
          template = @forms_templates.first
          expect(body['app_id']).to eq template.app_id
          # expect(body['uuid']).to eq template.uuid
          # expect(body['name']).to eq template.name
          expect(body['form']).not_to be_nil
        }
      end
    end
  end
end
