require 'swagger_helper'

RSpec.describe 'forms/admin/templates', type: :request, capture_examples: true, tags: ['forms admin'] do
  template_ref = {
    type: :object, properties: {
      template: {
        type: :object, properties: {
          uuid: { type: :string, description: '表单的唯一标识，可以替代id给前端使用' },
          name: { type: :string, description: '表单的名称' },
          form: { type: :jsonb, description: '' },
        },
      },
    },
  }
  template_value = FactoryBot.attributes_for(:forms_template)

  before :each do
    @user.add_role :com_admin
  end

  path '/forms/admin/templates/{id}/clone' do
    parameter 'id', in: :path, type: :string

    post(summary: 'clone template') do
      produces 'application/json'
      consumes 'application/json'
      response(201, description: 'successful') do
        let(:id) { @forms_templates.first.id }
        it {
          body = JSON.parse(response.body)
          template = @forms_templates.first
          expect(body['app_id']).to eq template.app_id
          expect(body['uuid']).not_to eq template.uuid
          expect(body['name']).not_to eq template.name
          # expect(body['form']).to eq template.form.as_json
        }
      end
    end
  end

  path '/forms/admin/templates' do
    get(summary: 'list templates') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq 2
        }
      end
    end

    post(summary: 'create template') do
      produces 'application/json'
      consumes 'application/json'
      parameter :template, in: :body, schema: template_ref
      response(201, description: 'successful') do
        let(:template) do
          { template: template_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq template_value[:app_id]
          # expect(body['uuid']).to eq template_value[:uuid]
          expect(body['name']).to eq template_value[:name]
          # expect(body['form']).to eq template_value[:form]
        }
      end
    end
  end

  path '/forms/admin/templates/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show template') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @forms_templates.first.id }
        it {
          body = JSON.parse(response.body)
          template = @forms_templates.first
          expect(body['app_id']).to eq template.app_id
          expect(body['uuid']).to eq template.uuid
          expect(body['name']).to eq template.name
          # expect(body['form']).to eq template.form.as_json
        }
      end
    end

    patch(summary: 'update template') do
      produces 'application/json'
      consumes 'application/json'
      parameter :template, in: :body, schema: template_ref
      response(201, description: 'successful') do
        let(:id) { @forms_templates.first.id }
        let(:template) do
          { template: template_value }
        end
      end
    end

    delete(summary: 'delete template') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @forms_templates.first.id }
        it {
          expect(Forms::Template.count).to eq(@forms_template_count - 1)
        }
      end
    end
  end
end
