require 'swagger_helper'

RSpec.describe 'com/user/search_items', type: :request, capture_examples: true, tags: ["com user"] do

  before :each do
  end

  path '/com/user/search_items' do

    get(summary: 'list search_items') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @com_search_item_count
        }
      end
    end
  end

  path '/com/user/search_items/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show search_item') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @com_search_items.first.id }
        it {
          body = JSON.parse(response.body)
          search_item = @com_search_items.first
          expect(body['app_id']).to eq search_item.app_id
          expect(body['model_flag']).to eq search_item.model_flag
          expect(body['model_payload']).to eq search_item.model_payload
          expect(body['model_payload_summary']).to eq search_item.model_payload_summary
          expect(body['name']).to eq search_item.name
          expect(body['position']).to eq search_item.position
          expect(body['group_name']).to eq search_item.group_name
          expect(body['enabled']).to eq search_item.enabled
          expect(body['conditions']).to eq search_item.conditions
        }
      end
    end
  end
end
