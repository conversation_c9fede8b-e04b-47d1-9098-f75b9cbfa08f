require 'swagger_helper'

RSpec.describe 'com/user/themes', type: :request, capture_examples: true, tags: ["com user"] do

  before :each do
  end

  path '/com/user/themes' do

    get(summary: 'list themes') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @com_theme_count
        }
      end
    end
  end

  path '/com/user/themes/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show theme') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @com_themes.first.id }
        it {
          body = JSON.parse(response.body)
          theme = @com_themes.first
          expect(body['app_id']).to eq theme.app_id
          expect(body['name']).to eq theme.name
          expect(body['conf']).to eq theme.conf
        }
      end
    end
  end
end
