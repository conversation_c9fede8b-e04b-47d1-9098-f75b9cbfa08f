require 'swagger_helper'

RSpec.describe 'com/user/model_settings', type: :request, capture_examples: true, tags: ["com user"] do
  before :each do
  end

  path '/com/user/model_settings' do

    get(summary: 'list model_settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @model_setting_count
        }
      end
    end
  end

  path '/com/user/model_settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show model_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @model_settings.first.id }
        it {
          body = JSON.parse(response.body)
          model_setting = @model_settings.first
          expect(body['model_define_id']).to eq model_setting.model_define_id
          expect(body['setable_type']).to eq model_setting.setable_type
          expect(body['setable_id']).to eq model_setting.setable_id
          expect(body['app_id']).to eq model_setting.app_id
          expect(body['forms_template_id']).to eq model_setting.forms_template_id
          expect(body['flag']).to eq model_setting.flag
          expect(body['flag_name']).to eq model_setting.flag_name
          # expect(body['form']).to eq model_setting.form
        }
      end
    end
  end
end
