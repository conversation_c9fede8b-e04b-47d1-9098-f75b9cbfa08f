require 'swagger_helper'

RSpec.describe 'com/user/async_tasks', type: :request, capture_examples: true, tags: ["com user"] do
  before :each do
  end

  path '/com/user/async_tasks' do

    get(summary: 'list async_tasks') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @async_task_count
        }
      end
    end
  end

  path '/com/user/async_tasks/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show async_task') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @async_tasks.first.id }
        it {
          body = JSON.parse(response.body)
          async_task = @async_tasks.first
          expect(body['app_id']).to eq async_task.app_id
          expect(body['user_id']).to eq async_task.user_id
          expect(body['taskable_type']).to eq async_task.taskable_type
          expect(body['taskable_id']).to eq async_task.taskable_id
          expect(body['model_flag']).to eq async_task.model_flag
          expect(body['model_payload']).to eq async_task.model_payload
          expect(body['model_payload_summary']).to eq async_task.model_payload_summary
          expect(body['model_detail']).to eq async_task.model_detail
          expect(body['seq']).to eq async_task.seq
          expect(body['type']).to eq async_task.type
          expect(body['flag']).to eq async_task.flag
          expect(body['name']).to eq async_task.name
          expect(body['progress']).to eq async_task.progress
          expect(body['state']).to eq async_task.state
          expect(body['perform_args']).to eq async_task.perform_args
          expect(body['options']).to eq async_task.options
          expect(body['payload']).to eq async_task.payload
          expect(body['result']).to eq async_task.result
          expect(body['meta']).to eq async_task.meta
        }
      end
    end

    delete(summary: 'delete async_task') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @async_tasks.first.id }
        it {
          expect(AsyncTask.count).to eq(@async_task_count-1)
        }
      end
    end
  end
end
