require 'swagger_helper'

RSpec.describe 'com/user/mods', type: :request, capture_examples: true, tags: ["com user"] do
  mod_ref = {
    type: :object, properties: {
      mod: {
        type: :object, properties: {
          name: { type: :string, description: '模块名称' },
          key: { type: :string, description: '模块对应查找的key值' },
        }
      }
    }
  }
  mod_value = FactoryBot.attributes_for(:mod)

  before :each do
  end

  path '/com/user/mods' do

    get(summary: 'list mods') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @mod_count
        }
      end
    end
  end

  path '/com/user/mods/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show mod') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @mods.first.id }
        it {
          body = JSON.parse(response.body)
          mod = @mods.first
          expect(body['name']).to eq mod.name
          expect(body['key']).to eq mod.key
        }
      end
    end
  end
end
