require 'swagger_helper'

RSpec.describe 'com/user/record_storages', type: :request, capture_examples: true, tags: ["com user"] do
  record_storage_ref = {
    type: :object, properties: {
      record_storage: {
        type: :object, properties: {
          key: { type: :string, description: '缓存区key' },
          storage: { type: :jsonb, description: '属性暂存区' },
        }
      }
    }
  }
  record_storage_value = FactoryBot.attributes_for(:com_record_storage)

  before :each do
  end

  path '/com/user/record_storages/fetch' do

    post(summary: 'fetch record_storage') do
      produces 'application/json'
      consumes 'application/json'
      parameter :record_storage, in: :body, schema: record_storage_ref
      response(201, description: 'successful') do
        let(:record_storage) do
          { record_storage: record_storage_value.merge(
            key: 'record_storage'
          ) }
        end
      end
    end
  end

  path '/com/user/record_storages/clear' do

    post(summary: 'clear record_storage') do
      produces 'application/json'
      consumes 'application/json'
      parameter :record_storage, in: :body, schema: record_storage_ref
      response(201, description: 'successful') do
        let(:record_storage) do
          { record_storage: record_storage_value.merge(
            key: 'record_storage'
          ) }
        end
      end
    end
  end

  path '/com/user/record_storages' do

    post(summary: 'create record_storage') do
      produces 'application/json'
      consumes 'application/json'
      parameter :record_storage, in: :body, schema: record_storage_ref
      response(201, description: 'successful') do
        let(:record_storage) do
          { record_storage: record_storage_value.merge(
            key: 'record_storage',
            storage: { test: '1' },
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['user_id']).to eq @user.id
          expect(body['key']).to eq 'record_storage'
          # expect(body['storage']).to eq record_storage_value[:storage]
        }
      end
    end
  end
end
