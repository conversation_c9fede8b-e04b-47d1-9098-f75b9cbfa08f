require 'swagger_helper'

RSpec.describe 'com/user/page_settings', type: :request, capture_examples: true, tags: ["com user"] do

  before :each do
  end

  path '/com/user/page_settings' do

    get(summary: 'list page_settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @page_setting_count
        }
      end
    end
  end

  path '/com/user/page_settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show page_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @page_settings.first.id }
        it {
          body = JSON.parse(response.body)
          page_setting = @page_settings.first
          expect(body['app_id']).to eq page_setting.app_id
          expect(body['seq']).to eq page_setting.seq
          expect(body['model_flag']).to eq page_setting.model_flag
          expect(body['model_payload']).to eq page_setting.model_payload
          expect(body['model_payload_summary']).to eq page_setting.model_payload_summary
          expect(body['name']).to eq page_setting.name
          expect(body['conf']).to eq page_setting.conf
        }
      end
    end
  end
end
