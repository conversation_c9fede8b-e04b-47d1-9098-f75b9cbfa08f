require 'swagger_helper'

RSpec.describe 'com/user/api_settings', type: :request, capture_examples: true, tags: ['com user'] do
  before :each do
  end

  path '/com/user/api_settings' do
    get(summary: 'list api_settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @api_setting_count
        }
      end
    end
  end

  path '/com/user/api_settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show api_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @api_settings.first.id }
        it {
          body = JSON.parse(response.body)
          api_setting = @api_settings.first
          expect(body['model_define_id']).to eq api_setting.model_define_id
          expect(body['app_id']).to eq api_setting.app_id
          expect(body['klass']).to eq api_setting.klass
          expect(body['action']).to eq api_setting.action
          expect(body['uid']).to eq api_setting.uid
          expect(body['extract_conf']).to eq api_setting.extract_conf
        }
      end
    end
  end
end
