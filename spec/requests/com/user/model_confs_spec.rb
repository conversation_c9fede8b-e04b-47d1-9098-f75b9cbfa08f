require 'swagger_helper'

RSpec.describe 'com/user/model_confs', type: :request, capture_examples: true, tags: ['com user'] do
  model_conf_ref = {
    type: :object, properties: {
      model_conf: {
        type: :object, properties: {
          model_define_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          klass: { type: :string, description: '类名' },
          conf: { type: :jsonb, description: '' },
        },
      },
    },
  }
  model_conf_value = FactoryBot.attributes_for(:model_conf)

  before :each do
  end

  path '/com/user/model_confs' do
    get(summary: 'list model_confs') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @model_conf_count
        }
      end
    end
  end

  path '/com/user/model_confs/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show model_conf') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @model_confs.first.id }
        it {
          body = JSON.parse(response.body)
          model_conf = @model_confs.first
          expect(body['model_define_id']).to eq model_conf.model_define_id
          expect(body['name']).to eq model_conf.name
          expect(body['klass']).to eq model_conf.klass
          expect(body['conf']).to eq model_conf.conf
        }
      end
    end
  end
end
