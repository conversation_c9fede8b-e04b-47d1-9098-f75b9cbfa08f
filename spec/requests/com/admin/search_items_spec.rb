require 'swagger_helper'

RSpec.describe 'com/admin/search_items', type: :request, capture_examples: true, tags: ["com admin"] do
  search_item_ref = {
    type: :object, properties: {
      search_item: {
        type: :object, properties: {
          name: { type: :string, description: '搜索条件 ' },
          position: { type: :string, description: '位置' },
          enabled: { type: :boolean, description: '是否启用' },
          conditions: { type: :jsonb, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          group_name: { type: :string, description: '分组标识' },
        }
      }
    }
  }
  search_item_value = FactoryBot.attributes_for(:com_search_item)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/search_items' do

    get(summary: 'list search_items') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @com_search_item_count
        }
      end
    end

    post(summary: 'create search_item') do
      produces 'application/json'
      consumes 'application/json'
      parameter :search_item, in: :body, schema: search_item_ref
      response(201, description: 'successful') do
        let(:search_item) do
          { search_item: search_item_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['name']).to eq search_item_value[:name]
          expect(body['position']).not_to be_nil
          expect(body['enabled']).to eq true
          expect(body['conditions']).to eq search_item_value[:conditions]
          expect(body['model_flag']).to eq search_item_value[:model_flag]
          expect(body['model_payload']).to eq search_item_value[:model_payload]
          expect(body['model_payload_summary']).to eq search_item_value[:model_payload_summary]
          expect(body['group_name']).to eq search_item_value[:group_name]
        }
      end
    end
  end

  path '/com/admin/search_items/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show search_item') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @com_search_items.first.id }
        it {
          body = JSON.parse(response.body)
          search_item = @com_search_items.first
          expect(body['app_id']).to eq search_item.app_id
          expect(body['name']).to eq search_item.name
          expect(body['position']).to eq search_item.position
          expect(body['enabled']).to eq search_item.enabled
          expect(body['conditions']).to eq search_item.conditions
          expect(body['model_flag']).to eq search_item.model_flag
          expect(body['model_payload']).to eq search_item.model_payload
          expect(body['model_payload_summary']).to eq search_item.model_payload_summary
          expect(body['group_name']).to eq search_item.group_name
        }
      end
    end

    patch(summary: 'update search_item') do
      produces 'application/json'
      consumes 'application/json'
      parameter :search_item, in: :body, schema: search_item_ref
      response(201, description: 'successful') do
        let(:id) { @com_search_items.first.id }
        let(:search_item) do
          { search_item: search_item_value }
        end
      end
    end

    delete(summary: 'delete search_item') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @com_search_items.first.id }
        it {
          expect(Com::SearchItem.count).to eq(@com_search_item_count-1)
        }
      end
    end
  end
end
