require 'swagger_helper'

RSpec.describe 'com/admin/model_settings', type: :request, capture_examples: true, tags: ["com admin"] do
  model_setting_ref = {
    type: :object, properties: {
      model_setting: {
        type: :object, properties: {
          model_define_id: { type: :integer, description: '' },
          setable_type: { type: :string, description: '' },
          setable_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          forms_template_id: { type: :integer, description: '' },
          flag: { type: :string, description: '同一个模型中的不同定义，其中model代表是这个对象的模型' },
          flag_name: { type: :string, description: 'flag对应中文名称' },
        }
      }
    }
  }
  model_setting_value = FactoryBot.attributes_for(:model_setting)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/model_defines/{model_define_id}/model_settings' do
    parameter 'model_define_id', in: :path, type: :string

    get(summary: 'list model_settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:model_define_id) { @model_define.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @model_setting_count
        }
      end
    end

    post(summary: 'create model_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :model_setting, in: :body, schema: model_setting_ref
      response(201, description: 'successful') do
        let(:model_define_id) { @model_define.id }
        let(:model_setting) do
          { model_setting: model_setting_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['model_define_id']).to eq @model_define.id
          expect(body['setable_type']).to eq model_setting_value[:setable_type]
          expect(body['setable_id']).to eq model_setting_value[:setable_id]
          expect(body['app_id']).to eq model_setting_value[:app_id]
          expect(body['forms_template_id']).to eq model_setting_value[:forms_template_id]
          expect(body['flag']).to eq model_setting_value[:flag]
          expect(body['flag_name']).to eq model_setting_value[:flag_name]
        }
      end
    end
  end

  path '/com/admin/model_settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show model_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @model_settings.first.id }
        it {
          body = JSON.parse(response.body)
          model_setting = @model_settings.first
          expect(body['model_define_id']).to eq model_setting.model_define_id
          expect(body['setable_type']).to eq model_setting.setable_type
          expect(body['setable_id']).to eq model_setting.setable_id
          expect(body['app_id']).to eq model_setting.app_id
          expect(body['forms_template_id']).to eq model_setting.forms_template_id
          expect(body['flag']).to eq model_setting.flag
          expect(body['flag_name']).to eq model_setting.flag_name
        }
      end
    end

    patch(summary: 'update model_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :model_setting, in: :body, schema: model_setting_ref
      response(201, description: 'successful') do
        let(:id) { @model_settings.first.id }
        let(:model_setting) do
          { model_setting: model_setting_value }
        end
      end
    end

    delete(summary: 'delete model_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @model_settings.first.id }
        it {
          expect(ModelSetting.count).to eq(@model_setting_count-1)
        }
      end
    end
  end
end
