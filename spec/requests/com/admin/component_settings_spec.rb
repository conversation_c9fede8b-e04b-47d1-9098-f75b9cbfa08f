require 'swagger_helper'

RSpec.describe 'com/admin/component_settings', type: :request, capture_examples: true, tags: ["com admin"] do
  component_setting_ref = {
    type: :object, properties: {
      component_setting: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          seq: { type: :string, description: '编号' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          name: { type: :string, description: '组件配置名称' },
          component_klass: { type: :string, description: '组件类名称' },
          component_path: { type: :string, description: '组件类路径' },
          conf: { type: :jsonb, description: '组件配置的json结构' },
        }
      }
    }
  }
  component_setting_value = FactoryBot.attributes_for(:component_setting)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/component_settings' do

    get(summary: 'list component_settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @component_setting_count
        }
      end
    end

    post(summary: 'create component_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :component_setting, in: :body, schema: component_setting_ref
      response(201, description: 'successful') do
        let(:component_setting) do
          { component_setting: component_setting_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq component_setting_value[:app_id]
          expect(body['seq']).not_to be_nil
          expect(body['model_flag']).to eq component_setting_value[:model_flag]
          expect(body['model_payload']).to eq component_setting_value[:model_payload]
          expect(body['model_payload_summary']).to eq component_setting_value[:model_payload_summary]
          expect(body['name']).to eq component_setting_value[:name]
          expect(body['component_klass']).to eq component_setting_value[:component_klass]
          expect(body['component_path']).to eq component_setting_value[:component_path]
          expect(body['conf']).to eq component_setting_value[:conf]
        }
      end
    end
  end

  path '/com/admin/component_settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show component_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @component_settings.first.id }
        it {
          body = JSON.parse(response.body)
          component_setting = @component_settings.first
          expect(body['app_id']).to eq component_setting.app_id
          expect(body['seq']).to eq component_setting.seq
          expect(body['model_flag']).to eq component_setting.model_flag
          expect(body['model_payload']).to eq component_setting.model_payload
          expect(body['model_payload_summary']).to eq component_setting.model_payload_summary
          expect(body['name']).to eq component_setting.name
          expect(body['component_klass']).to eq component_setting.component_klass
          expect(body['component_path']).to eq component_setting.component_path
          expect(body['conf']).to eq component_setting.conf
        }
      end
    end

    patch(summary: 'update component_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :component_setting, in: :body, schema: component_setting_ref
      response(201, description: 'successful') do
        let(:id) { @component_settings.first.id }
        let(:component_setting) do
          { component_setting: component_setting_value }
        end
      end
    end

    delete(summary: 'delete component_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @component_settings.first.id }
        it {
          expect(ComponentSetting.count).to eq(@component_setting_count-1)
        }
      end
    end
  end
end
