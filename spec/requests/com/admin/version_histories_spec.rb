require 'swagger_helper'

RSpec.describe 'com/admin/version_histories', type: :request, capture_examples: true, tags: ["com admin"] do
  version_history_ref = {
    type: :object, properties: {
      version_history: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          name: { type: :string, description: '版本发布名称' },
          version: { type: :string, description: '版本号' },
          content: { type: :jsonb, description: '发布说明' },
          position: { type: :integer, description: '发布顺序' },
        }
      }
    }
  }
  version_history_value = FactoryBot.attributes_for(:com_version_history)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/version_histories' do

    get(summary: 'list version_histories') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @com_version_history_count
        }
      end
    end

    post(summary: 'create version_history') do
      produces 'application/json'
      consumes 'application/json'
      parameter :version_history, in: :body, schema: version_history_ref
      response(201, description: 'successful') do
        let(:version_history) do
          { version_history: version_history_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['creator_id']).to eq @user.id
          expect(body['model_flag']).to eq version_history_value[:model_flag]
          expect(body['model_payload']).to eq version_history_value[:model_payload]
          expect(body['model_payload_summary']).to eq version_history_value[:model_payload_summary]
          expect(body['name']).to eq version_history_value[:name]
          expect(body['version']).to eq version_history_value[:version]
          expect(body['content']).to eq version_history_value[:content]
          expect(body['position']).not_to be_nil
        }
      end
    end
  end

  path '/com/admin/version_histories/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show version_history') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @com_version_histories.first.id }
        it {
          body = JSON.parse(response.body)
          version_history = @com_version_histories.first
          expect(body['app_id']).to eq version_history.app_id
          expect(body['creator_id']).to eq version_history.creator_id
          expect(body['model_flag']).to eq version_history.model_flag
          expect(body['model_payload']).to eq version_history.model_payload
          expect(body['model_payload_summary']).to eq version_history.model_payload_summary
          expect(body['name']).to eq version_history.name
          expect(body['version']).to eq version_history.version
          expect(body['content']).to eq version_history.content
          expect(body['position']).to eq version_history.position
        }
      end
    end

    patch(summary: 'update version_history') do
      produces 'application/json'
      consumes 'application/json'
      parameter :version_history, in: :body, schema: version_history_ref
      response(201, description: 'successful') do
        let(:id) { @com_version_histories.first.id }
        let(:version_history) do
          { version_history: version_history_value }
        end
      end
    end

    delete(summary: 'delete version_history') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @com_version_histories.first.id }
        it {
          expect(Com::VersionHistory.count).to eq(@com_version_history_count-1)
        }
      end
    end
  end
end
