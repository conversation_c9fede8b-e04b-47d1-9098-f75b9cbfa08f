require 'swagger_helper'

RSpec.describe 'com/admin/mods', type: :request, capture_examples: true, tags: ["com admin"] do
  mod_ref = {
    type: :object, properties: {
      mod: {
        type: :object, properties: {
          name: { type: :string, description: '模块名称' },
          key: { type: :string, description: '模块对应查找的key值' },
        }
      }
    }
  }
  mod_value = FactoryBot.attributes_for(:mod)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/mods' do

    get(summary: 'list mods') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @mod_count
        }
      end
    end

    post(summary: 'create mod') do
      produces 'application/json'
      consumes 'application/json'
      parameter :mod, in: :body, schema: mod_ref
      response(201, description: 'successful') do
        let(:mod) do
          { mod: mod_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['name']).to eq mod_value[:name]
          expect(body['key']).to eq mod_value[:key]
        }
      end
    end
  end

  path '/com/admin/mods/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show mod') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @mods.first.id }
        it {
          body = JSON.parse(response.body)
          mod = @mods.first
          expect(body['name']).to eq mod.name
          expect(body['key']).to eq mod.key
        }
      end
    end

    patch(summary: 'update mod') do
      produces 'application/json'
      consumes 'application/json'
      parameter :mod, in: :body, schema: mod_ref
      response(201, description: 'successful') do
        let(:id) { @mods.first.id }
        let(:mod) do
          { mod: mod_value }
        end
      end
    end

    delete(summary: 'delete mod') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @mods.first.id }
        it {
          expect(Mod.count).to eq(@mod_count-1)
        }
      end
    end
  end
end
