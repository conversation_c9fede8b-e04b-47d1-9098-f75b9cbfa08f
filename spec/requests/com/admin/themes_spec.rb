require 'swagger_helper'

RSpec.describe 'com/admin/themes', type: :request, capture_examples: true, tags: ["com admin"] do
  theme_ref = {
    type: :object, properties: {
      theme: {
        type: :object, properties: {
          name: { type: :string, description: '主题名称' },
          conf: { type: :jsonb, description: '主题配置' },
        }
      }
    }
  }
  theme_value = FactoryBot.attributes_for(:com_theme)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/apps/{app_id}/themes' do
    parameter 'app_id', in: :path, type: :string

    get(summary: 'list themes') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:app_id) { @app.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @com_theme_count
        }
      end
    end

    post(summary: 'create theme') do
      produces 'application/json'
      consumes 'application/json'
      parameter :theme, in: :body, schema: theme_ref
      response(201, description: 'successful') do
        let(:app_id) { @app.id }
        let(:theme) do
          { theme: theme_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['name']).to eq theme_value[:name]
          expect(body['conf']).to eq theme_value[:conf]
        }
      end
    end
  end

  path '/com/admin/apps/{app_id}/themes/{id}' do
    parameter 'app_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show theme') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:app_id) { @app.id }
        let(:id) { @com_themes.first.id }
        it {
          body = JSON.parse(response.body)
          theme = @com_themes.first
          expect(body['app_id']).to eq theme.app_id
          expect(body['name']).to eq theme.name
          expect(body['conf']).to eq theme.conf
        }
      end
    end

    patch(summary: 'update theme') do
      produces 'application/json'
      consumes 'application/json'
      parameter :theme, in: :body, schema: theme_ref
      response(201, description: 'successful') do
        let(:app_id) { @app.id }
        let(:id) { @com_themes.first.id }
        let(:theme) do
          { theme: theme_value }
        end
      end
    end

    delete(summary: 'delete theme') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:app_id) { @app.id }
        let(:id) { @com_themes.first.id }
        it {
          expect(Com::Theme.count).to eq(@com_theme_count-1)
        }
      end
    end
  end
end
