require 'swagger_helper'

RSpec.describe 'com/admin/model_confs', type: :request, capture_examples: true, tags: ['com admin'] do
  model_conf_ref = {
    type: :object, properties: {
      model_conf: {
        type: :object, properties: {
          model_define_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          klass: { type: :string, description: '类名' },
          conf: { type: :jsonb, description: '' },
        },
      },
    },
  }
  model_conf_value = FactoryBot.attributes_for(:model_conf)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/model_confs' do
    get(summary: 'list model_confs') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @model_conf_count
        }
      end
    end

    post(summary: 'create model_conf') do
      produces 'application/json'
      consumes 'application/json'
      parameter :model_conf, in: :body, schema: model_conf_ref
      response(201, description: 'successful') do
        let(:model_conf) do
          { model_conf: model_conf_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['model_define_id']).to eq model_conf_value[:model_define_id]
          expect(body['name']).to eq model_conf_value[:name]
          expect(body['klass']).to eq model_conf_value[:klass]
          expect(body['conf']).to eq model_conf_value[:conf]
        }
      end
    end
  end

  path '/com/admin/model_confs/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show model_conf') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @model_confs.first.id }
        it {
          body = JSON.parse(response.body)
          model_conf = @model_confs.first
          expect(body['model_define_id']).to eq model_conf.model_define_id
          expect(body['name']).to eq model_conf.name
          expect(body['klass']).to eq model_conf.klass
          expect(body['conf']).to eq model_conf.conf
        }
      end
    end

    patch(summary: 'update model_conf') do
      produces 'application/json'
      consumes 'application/json'
      parameter :model_conf, in: :body, schema: model_conf_ref
      response(201, description: 'successful') do
        let(:id) { @model_confs.first.id }
        let(:model_conf) do
          { model_conf: model_conf_value }
        end
      end
    end

    delete(summary: 'delete model_conf') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @model_confs.first.id }
        it {
          expect(ModelConf.count).to eq(@model_conf_count - 1)
        }
      end
    end
  end
end
