require 'swagger_helper'

RSpec.describe 'com/admin/roles', type: :request, capture_examples: true, tags: ["com admin"] do
  # role_ref = {
  #   type: :object, properties: {
  #     role: {
  #       type: :object, properties: {
  #         name: { type: :string, description: '' },
  #       }
  #     }
  #   }
  # }
  # role_value = FactoryBot.attributes_for(:role)

  before :each do
    @user.add_role :tofu_admin
  end

  path '/com/admin/roles' do

    # get(summary: 'list roles') do
    #   produces 'application/json'
    #   consumes 'application/json'
    #   response(200, description: 'successful') do
    #     it {
    #       body = JSON.parse(response.body)
    #       expect(body['records'].count).to eq @role_count
    #     }
    #   end
    # end

    # post(summary: 'create role') do
    #   produces 'application/json'
    #   consumes 'application/json'
    #   parameter :role, in: :body, schema: role_ref
    #   response(201, description: 'successful') do
    #     let(:role) do
    #       { role: role_value }
    #     end
    #     it {
    #       body = JSON.parse(response.body)
    #       expect(body['resource_type']).to eq role_value[:resource_type]
    #       expect(body['resource_id']).to eq role_value[:resource_id]
    #       expect(body['name']).to eq role_value[:name]
    #     }
    #   end
    # end
  end

  # path '/com/admin/roles/{id}' do
  #   parameter 'id', in: :path, type: :string
  #
  #   get(summary: 'show role') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     response(200, description: 'successful') do
  #       let(:id) { @roles.first.id }
  #       it {
  #         body = JSON.parse(response.body)
  #         role = @roles.first
  #         expect(body['resource_type']).to eq role.resource_type
  #         expect(body['resource_id']).to eq role.resource_id
  #         expect(body['name']).to eq role.name
  #       }
  #     end
  #   end

    # patch(summary: 'update role') do
    #   produces 'application/json'
    #   consumes 'application/json'
    #   parameter :role, in: :body, schema: role_ref
    #   response(201, description: 'successful') do
    #     let(:id) { @roles.first.id }
    #     let(:role) do
    #       { role: role_value }
    #     end
    #   end
    # end

    # delete(summary: 'delete role') do
    #   produces 'application/json'
    #   consumes 'application/json'
    #   response(204, description: 'successful') do
    #     let(:id) { @roles.first.id }
    #     it {
    #       expect(Role.count).to eq(@role_count-1)
    #     }
    #   end
    # end
  # end
end
