require 'swagger_helper'

RSpec.describe 'com/admin/api_settings', type: :request, capture_examples: true, tags: ['com admin'] do
  api_setting_ref = {
    type: :object, properties: {
      api_setting: {
        type: :object, properties: {
          model_define_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          klass: { type: :string, description: '对应的active record class name' },
          action: { type: :string, description: '对应controller的action' },
          uid: { type: :string, description: '自动生成的唯一标识' },
          extract_conf: { type: :jsonb, description: '' },
        },
      },
    },
  }
  api_setting_value = FactoryBot.attributes_for(:api_setting)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/api_settings' do
    get(summary: 'list api_settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @api_setting_count
        }
      end
    end

    post(summary: 'create api_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :api_setting, in: :body, schema: api_setting_ref
      response(201, description: 'successful') do
        let(:api_setting) do
          { api_setting: api_setting_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['model_define_id']).to eq api_setting_value[:model_define_id]
          expect(body['app_id']).to eq api_setting_value[:app_id]
          expect(body['klass']).to eq api_setting_value[:klass]
          expect(body['action']).to eq api_setting_value[:action]
          expect(body['uid']).to eq api_setting_value[:uid]
          expect(body['extract_conf']).to eq api_setting_value[:extract_conf]
        }
      end
    end
  end

  path '/com/admin/api_settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show api_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @api_settings.first.id }
        it {
          body = JSON.parse(response.body)
          api_setting = @api_settings.first
          expect(body['model_define_id']).to eq api_setting.model_define_id
          expect(body['app_id']).to eq api_setting.app_id
          expect(body['klass']).to eq api_setting.klass
          expect(body['action']).to eq api_setting.action
          expect(body['uid']).to eq api_setting.uid
          expect(body['extract_conf']).to eq api_setting.extract_conf
        }
      end
    end

    patch(summary: 'update api_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :api_setting, in: :body, schema: api_setting_ref
      response(201, description: 'successful') do
        let(:id) { @api_settings.first.id }
        let(:api_setting) do
          { api_setting: api_setting_value }
        end
      end
    end

    delete(summary: 'delete api_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @api_settings.first.id }
        it {
          expect(ApiSetting.count).to eq(@api_setting_count - 1)
        }
      end
    end
  end
end
