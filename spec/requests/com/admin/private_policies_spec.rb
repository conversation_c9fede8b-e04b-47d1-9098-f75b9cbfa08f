require 'swagger_helper'

RSpec.describe 'com/admin/private_policies', type: :request, capture_examples: true, tags: ["com admin"] do
  private_policy_ref = {
    type: :object, properties: {
      private_policy: {
        type: :object, properties: {
          name: { type: :string, description: '条款名称' },
          key: { type: :string, description: '关键字，可能有不同业务模块需要使用的关键字' },
          content: { type: :jsonb, description: '隐私条款内容' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  private_policy_value = FactoryBot.attributes_for(:com_private_policy)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/private_policies' do

    get(summary: 'list private_policies') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @com_private_policy_count
        }
      end
    end

    post(summary: 'create private_policy') do
      produces 'application/json'
      consumes 'application/json'
      parameter :private_policy, in: :body, schema: private_policy_ref
      response(201, description: 'successful') do
        let(:private_policy) do
          { private_policy: private_policy_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['name']).to eq private_policy_value[:name]
          expect(body['key']).to eq private_policy_value[:key]
          expect(body['content']).to eq private_policy_value[:content]
          expect(body['position']).not_to be_nil
        }
      end
    end
  end

  path '/com/admin/private_policies/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show private_policy') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @com_private_policies.first.id }
        it {
          body = JSON.parse(response.body)
          private_policy = @com_private_policies.first
          expect(body['app_id']).to eq private_policy.app_id
          expect(body['name']).to eq private_policy.name
          expect(body['key']).to eq private_policy.key
          expect(body['content']).to eq private_policy.content
          expect(body['position']).to eq private_policy.position
        }
      end
    end

    patch(summary: 'update private_policy') do
      produces 'application/json'
      consumes 'application/json'
      parameter :private_policy, in: :body, schema: private_policy_ref
      response(201, description: 'successful') do
        let(:id) { @com_private_policies.first.id }
        let(:private_policy) do
          { private_policy: private_policy_value }
        end
      end
    end

    delete(summary: 'delete private_policy') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @com_private_policies.first.id }
        it {
          expect(Com::PrivatePolicy.count).to eq(@com_private_policy_count-1)
        }
      end
    end
  end
end
