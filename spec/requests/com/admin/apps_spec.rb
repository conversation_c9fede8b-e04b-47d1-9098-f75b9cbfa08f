require 'swagger_helper'

RSpec.describe 'com/admin/apps', type: :request, capture_examples: true, tags: ['com admin'] do
  app_ref = {
    type: :object, properties: {
      app: {
        type: :object, properties: {
          code: { type: :string, description: '应用标识' },
          name: { type: :string, description: '应用的名称' },
        },
      },
    },
  }
  app_value = FactoryBot.attributes_for(:app)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/apps' do
    # parameter 'iv-decode', in: :header, type: :string
    # parameter 'iv-encrypt', in: :header, type: :string
    # parameter 'authorization', in: :header, type: :string

    get(summary: 'list apps') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        # let('iv-decode') { "iv decode" }
        # let('authorization') { "authorization" }
        # let('iv-encrypt') { true }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @app_count
        }
      end
    end

    post(summary: 'create app') do
      produces 'application/json'
      consumes 'application/json'
      parameter :app_params, in: :body, schema: app_ref
      response(201, description: 'successful') do
        let(:app_params) do
          { app: app_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['code']).to eq app_value[:code]
          expect(body['name']).to eq app_value[:name]
        }
      end
    end
  end

  path '/com/admin/apps/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show app') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @apps.first.id }
        it {
          body = JSON.parse(response.body)
          app = @apps.first
          expect(body['code']).to eq app.code
          expect(body['name']).to eq app.name
        }
      end
    end

    patch(summary: 'update app') do
      produces 'application/json'
      consumes 'application/json'
      parameter :app_params, in: :body, schema: app_ref
      response(201, description: 'successful') do
        let(:id) { @apps.first.id }
        let(:app_params) do
          { app: app_value }
        end
      end
    end

    # delete(summary: 'delete app') do
    #   produces 'application/json'
    #   consumes 'application/json'
    #   response(204, description: 'successful') do
    #     let(:id) { @apps.first.id }
    #     it {
    #       expect(App.count).to eq(@app_count-1)
    #     }
    #   end
    # end
  end
end
