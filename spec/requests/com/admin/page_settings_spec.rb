require 'swagger_helper'

RSpec.describe 'com/admin/page_settings', type: :request, capture_examples: true, tags: ["com admin"] do
  page_setting_ref = {
    type: :object, properties: {
      page_setting: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          seq: { type: :string, description: '编号' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          name: { type: :string, description: '页面配置名称' },
          conf: { type: :jsonb, description: '页面配置的json结构' },
        }
      }
    }
  }
  page_setting_value = FactoryBot.attributes_for(:page_setting)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/page_settings' do

    get(summary: 'list page_settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @page_setting_count
        }
      end
    end

    post(summary: 'create page_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :page_setting, in: :body, schema: page_setting_ref
      response(201, description: 'successful') do
        let(:page_setting) do
          { page_setting: page_setting_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq page_setting_value[:app_id]
          expect(body['seq']).not_to be_nil
          expect(body['model_flag']).to eq page_setting_value[:model_flag]
          expect(body['model_payload']).to eq page_setting_value[:model_payload]
          expect(body['model_payload_summary']).to eq page_setting_value[:model_payload_summary]
          expect(body['name']).to eq page_setting_value[:name]
          expect(body['conf']).to eq page_setting_value[:conf]
        }
      end
    end
  end

  path '/com/admin/page_settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show page_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @page_settings.first.id }
        it {
          body = JSON.parse(response.body)
          page_setting = @page_settings.first
          expect(body['app_id']).to eq page_setting.app_id
          expect(body['seq']).to eq page_setting.seq
          expect(body['model_flag']).to eq page_setting.model_flag
          expect(body['model_payload']).to eq page_setting.model_payload
          expect(body['model_payload_summary']).to eq page_setting.model_payload_summary
          expect(body['name']).to eq page_setting.name
          expect(body['conf']).to eq page_setting.conf
        }
      end
    end

    patch(summary: 'update page_setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :page_setting, in: :body, schema: page_setting_ref
      response(201, description: 'successful') do
        let(:id) { @page_settings.first.id }
        let(:page_setting) do
          { page_setting: page_setting_value }
        end
      end
    end

    delete(summary: 'delete page_setting') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @page_settings.first.id }
        it {
          expect(PageSetting.count).to eq(@page_setting_count-1)
        }
      end
    end
  end
end
