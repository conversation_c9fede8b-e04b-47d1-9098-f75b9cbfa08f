require 'swagger_helper'

RSpec.describe 'com/admin/model_defines', type: :request, capture_examples: true, tags: ["com admin"] do
  model_define_ref = {
    type: :object, properties: {
      model_define: {
        type: :object, properties: {
          klass: { type: :string, description: '对应设置的Model名称' },
          name: { type: :string, description: '模型设置的中文名' },
          association_chain: { type: :jsonb, description: '查找的关系列表' },
          klass_singular: { type: :string, description: '自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找' },
        }
      }
    }
  }
  model_define_value = FactoryBot.attributes_for(:model_define)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/model_defines' do

    get(summary: 'list model_defines') do
      produces 'application/json'
      consumes 'application/json'
      parameter :page, in: :query, type: :string

      response(200, description: 'successful') do
        let(:page) { 1 }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @model_define_count
        }
      end
    end

    post(summary: 'create model_define') do
      produces 'application/json'
      consumes 'application/json'
      parameter :model_define, in: :body, schema: model_define_ref
      response(201, description: 'successful') do
        let(:model_define) do
          { model_define: model_define_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['klass']).to eq model_define_value[:klass]
          expect(body['name']).to eq model_define_value[:name]
          expect(body['association_chain']).to eq model_define_value[:association_chain]
          expect(body['klass_singular']).to eq 'model_define'
        }
      end
    end
  end

  path '/com/admin/model_defines/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show model_define') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @model_defines.first.id }
        it {
          body = JSON.parse(response.body)
          model_define = @model_defines.first
          expect(body['klass']).to eq model_define.klass
          expect(body['name']).to eq model_define.name
          expect(body['association_chain']).to eq model_define.association_chain
          expect(body['klass_singular']).to eq model_define.klass_singular
        }
      end
    end

    patch(summary: 'update model_define') do
      produces 'application/json'
      consumes 'application/json'
      parameter :model_define, in: :body, schema: model_define_ref
      response(201, description: 'successful') do
        let(:id) { @model_defines.first.id }
        let(:model_define) do
          { model_define: model_define_value }
        end
      end
    end

    delete(summary: 'delete model_define') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @model_defines.first.id }
        it {
          expect(ModelDefine.count).to eq(@model_define_count-1)
        }
      end
    end
  end
end
