require 'swagger_helper'

RSpec.describe 'com/admin/data_forms', type: :request, capture_examples: true, tags: ['com admin'] do
  data_form_ref = {
    type: :object, properties: {
      data_form: {
        type: :object, properties: {
          create_user_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          record_type: { type: :string, description: '' },
          record_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          source_flag: { type: :string, description: '关联source的flag' },
          state: { type: :string, description: '数据状态' },
          payload: { type: :jsonb, description: '存储的信息' },
          summary: { type: :jsonb, description: '通过form生成的缩略信息' },
          form_conf: { type: :jsonb, description: '' },
          options: { type: :jsonb, description: '额外的数据信息' },
          meta: { type: :jsonb, description: '预留后续的数据存储' },
        },
      },
    },
  }
  data_form_value = FactoryBot.attributes_for(:data_form)

  before :each do
    @user.add_role :com_admin
  end

  path '/com/admin/data_forms' do
    get(summary: 'list data_forms') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @data_form_count
        }
      end
    end

    post(summary: 'create data_form') do
      produces 'application/json'
      consumes 'application/json'
      parameter :data_form, in: :body, schema: data_form_ref
      response(201, description: 'successful') do
        let(:data_form) do
          { data_form: data_form_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['create_user_id']).to eq data_form_value[:create_user_id]
          expect(body['source_type']).to eq data_form_value[:source_type]
          expect(body['source_id']).to eq data_form_value[:source_id]
          expect(body['record_type']).to eq data_form_value[:record_type]
          expect(body['record_id']).to eq data_form_value[:record_id]
          expect(body['type']).to eq data_form_value[:type]
          expect(body['model_flag']).to eq data_form_value[:model_flag]
          expect(body['model_payload']).to eq data_form_value[:model_payload]
          expect(body['model_payload_summary']).to eq data_form_value[:model_payload_summary]
          expect(body['model_detail']).to eq data_form_value[:model_detail]
          expect(body['source_flag']).to eq data_form_value[:source_flag]
          expect(body['state']).to eq data_form_value[:state]
          # expect(body['payload']).to eq data_form_value[:payload]
          expect(body['summary']).to eq data_form_value[:summary]
          expect(body['form_conf']).to eq data_form_value[:form_conf]
          expect(body['options']).to eq data_form_value[:options]
          expect(body['meta']).to eq data_form_value[:meta]
        }
      end
    end
  end

  path '/com/admin/data_forms/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show data_form') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @data_forms.first.id }
        it {
          body = JSON.parse(response.body)
          data_form = @data_forms.first
          expect(body['app_id']).to eq data_form.app_id
          expect(body['create_user_id']).to eq data_form.create_user_id
          expect(body['source_type']).to eq data_form.source_type
          expect(body['source_id']).to eq data_form.source_id
          expect(body['record_type']).to eq data_form.record_type
          expect(body['record_id']).to eq data_form.record_id
          expect(body['type']).to eq data_form.type
          expect(body['model_flag']).to eq data_form.model_flag
          expect(body['model_payload']).to eq data_form.model_payload
          expect(body['model_payload_summary']).to eq data_form.model_payload_summary
          expect(body['model_detail']).to eq data_form.model_detail
          expect(body['source_flag']).to eq data_form.source_flag
          expect(body['state']).to eq data_form.state
          expect(body['payload']).to eq data_form.payload
          expect(body['summary']).to eq data_form.summary
          expect(body['form_conf']).to eq data_form.form_conf
          expect(body['options']).to eq data_form.options
          expect(body['meta']).to eq data_form.meta
        }
      end
    end

    patch(summary: 'update data_form') do
      produces 'application/json'
      consumes 'application/json'
      parameter :data_form, in: :body, schema: data_form_ref
      response(201, description: 'successful') do
        let(:id) { @data_forms.first.id }
        let(:data_form) do
          { data_form: data_form_value }
        end
      end
    end

    delete(summary: 'delete data_form') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @data_forms.first.id }
        it {
          expect(DataForm.count).to eq(@data_form_count - 1)
        }
      end
    end
  end
end
