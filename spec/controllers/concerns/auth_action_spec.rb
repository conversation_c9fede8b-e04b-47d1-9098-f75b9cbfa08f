require 'rails_helper'

RSpec.describe ActionController::AuthAction do
  let(:app) { create(:app) }
  let(:user) { create(:user, app: app) }
  let(:token) { 'test_token' }
  let(:auth_response) do
    {
      'id' => 1,
      'account_type' => 'User',
      'account' => user.account,
      'app_id' => app.code,
      'user_id' => user.id
    }
  end

  controller(ActionController::Base) do
    include ActionController::AuthAction
  end

  describe 'authentication flow' do
    controller do
      auth_action :user

      def index
        render json: { user_id: current_user.id }
      end
    end

    before do
      allow(User).to receive(:auth!).with(token).and_return(auth_response)
      request.env['HTTP_AUTHORIZATION'] = "Token #{token}"
    end

    context 'with valid token' do
      it 'authenticates successfully' do
        get :index
        expect(response).to have_http_status(:success)
        expect(JSON.parse(response.body)['user_id']).to eq user.id
      end

      it 'sets current_user' do
        get :index
        expect(controller.current_user).to eq user
      end

      it 'updates last_visit_at' do
        expect { get :index }.to change { user.reload.last_visit_at }
      end
    end

    context 'with invalid token' do
      before do
        allow(User).to receive(:auth!).and_raise(Error::AuthError)
      end

      it 'raises auth error' do
        expect { get :index }.to raise_error(Error::AuthError)
      end
    end

    context 'with blocked user' do
      before do
        user.update(is_blocked: true)
      end

      it 'raises auth error' do
        expect { get :index }.to raise_error(Error::AuthError, '账号已锁定')
      end
    end
  end

  describe 'with custom validator' do
    controller do
      auth_action :user, validator: -> { raise Error::AuthError if current_user.is_blocked }

      def index
        render json: { status: 'ok' }
      end
    end

    before do
      allow(User).to receive(:auth!).with(token).and_return(auth_response)
      request.env['HTTP_AUTHORIZATION'] = "Token #{token}"
    end

    context 'when validation passes' do
      before do
        # 确保在 auth! 之后返回的用户是未锁定的
        allow_any_instance_of(User).to receive(:is_blocked).and_return(false)
      end

      it 'allows access' do
        get :index
        expect(response).to have_http_status(:success)
      end
    end

    context 'when validation fails' do
      before do
        # 使用 allow_any_instance_of 来确保所有 User 实例都会返回 is_blocked 为 true
        allow_any_instance_of(User).to receive(:is_blocked).and_return(true)
      end

      it 'raises auth error' do
        expect { get :index }.to raise_error(Error::AuthError)
      end
    end
  end

  describe 'with skip_error option' do
    controller do
      auth_action :user, skip_error: true

      def index
        render json: { auth: current_auth.present? }
      end
    end

    before do
      allow(User).to receive(:auth!).and_raise(Error::AuthError)
      request.env['HTTP_AUTHORIZATION'] = "Token invalid_token"
    end

    it 'does not raise error on authentication failure' do
      expect { get :index }.not_to raise_error
    end

    it 'sets current_app from HTTP_APPCODE' do
      request.env['HTTP_APPCODE'] = app.code
      get :index
      expect(controller.current_app).to eq app
    end
  end

  describe 'request_token' do
    controller do
      def index
        render json: { token: request_token }
      end
    end

    it 'extracts token from authorization header' do
      request.env['HTTP_AUTHORIZATION'] = 'Token abc123'
      get :index
      expect(JSON.parse(response.body)['token']).to eq 'abc123'
    end

    it 'returns empty string for missing authorization header' do
      get :index
      expect(JSON.parse(response.body)['token']).to eq ''
    end
  end
end
