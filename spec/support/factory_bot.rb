# RSpec
# spec/support/factory_bot.rb
RSpec.configure do |config|
  factories_directory = Rails.root.join('..', 'factories')
  FactoryBot.definition_file_paths = [factories_directory]
  FactoryBot.find_definitions
  config.include FactoryBot::Syntax::Methods
end

# Ensure the after_initialize callback is run during factory creation
FactoryBot::Strategy::Create.class_eval do
  def result(evaluation)
    instance = evaluation.object
    instance.run_callbacks(:initialize)
    instance.save!
    instance
  end
end

FactoryBot::Strategy::Build.class_eval do
  def result(evaluation)
    instance = evaluation.object
    instance.run_callbacks(:initialize)
    instance
  end
end

FactoryBot::Strategy::AttributesFor.class_eval do
  def result(evaluation)
    evaluation.object.tap do |instance|
      instance.run_callbacks(:initialize)
    end.attributes
  end
end
