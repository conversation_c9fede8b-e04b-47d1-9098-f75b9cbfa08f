RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  config.before(:each) do |example|
    @modules = example.metadata[:tags]&.first&.split(' ') || []

    # 创建app
    create_list :app, 2
    @apps = App.all
    @app = @apps.first
    @app_count = @apps.count

    create_list :mod, 2
    @mods = Mod.all
    @mod = @mods.first
    @mod_count = @mods.count

    # 创建用户
    create_list :user, 2, app: @app
    @users = User.all
    @user = @users.first
    @user_count = @users.count

    user_account_info = {
      account_type: 'User',
      account: @user.account,
    }
    allow(User).to receive(:auth!).and_return(user_account_info)

    # 创建模型定义
    create :model_define, klass: 'User'
    create :model_define, klass: 'App'
    @model_defines = ModelDefine.all
    @model_define = @model_defines.first
    @model_define_count = @model_defines.count

    create :model_setting, model_define: @model_define, flag: 'model'
    create :model_setting, model_define: @model_define, flag: 'flag1'
    @model_settings = ModelSetting.all
    @model_setting = @model_settings.first
    @model_setting_count = @model_settings.count

    create_list :page_setting, 2
    @page_settings = PageSetting.all
    @page_setting = @page_settings.first
    @page_setting_count = @page_settings.count

    create_list :component_setting, 2
    @component_settings = ComponentSetting.all
    @component_setting = @component_settings.first
    @component_setting_count = @component_settings.count

    # 表单配置
    create_list :forms_template, 2
    create_list :forms_template, 2, app: @app
    @forms_templates = Forms::Template.all
    @forms_template = @forms_templates.first
    @forms_template_count = @forms_templates.count

    # Role
    @roles = Role.all
    @role = @roles.first
    @role_count = @roles.count

    # 关联表单和模型
    @model_setting.update forms_template: @forms_template

    create_list :com_theme, 2, app: @app
    @com_themes = Com::Theme.all
    @com_theme = @com_themes.first
    @com_theme_count = @com_themes.count

    create_list :com_search_item, 2, app: @app
    @com_search_items = Com::SearchItem.all
    @com_search_item = @com_search_items.first
    @com_search_item_count = @com_search_items.count

    create_list :com_record_storage, 2, key: 'record_storage', user: @user
    @com_record_storages = Com::RecordStorage.all
    @com_record_storage = @com_record_storages.first
    @com_record_storage_count = @com_record_storages.count

    create_list :com_private_policy, 5, app: @app
    @com_private_policies = Com::PrivatePolicy.all
    @com_private_policy = @com_private_policies.first
    @com_private_policy_count = @com_private_policies.count

    create_list :com_version_history, 2, app: @app
    @com_version_histories = Com::VersionHistory.all
    @com_version_history = @com_version_histories.first
    @com_version_history_count = @com_version_histories.count

    @version_relationship_count = 5
    @version_relationships = create_list(
      :version_relationship,
      @version_relationship_count,
      resource: @com_version_history,
      real_resource: @com_private_policy,
      version: @com_private_policy,
    )

    create_list :data_form, 2, app: @app, create_user: @user
    @data_forms = DataForm.all
    @data_form = @data_forms.first
    @data_form_count = @data_forms.count

    caculation = Com::Attr::Stat::Caculation.new(
      caculations: [
        {
          name: '求和',
          attr: 'id',
          method: 'sum',
        },
        {
          name: '最大值',
          attr: 'id',
          method: 'maximum',
        },
      ],
    )
    User.ta_statistic(caculation)

    group_calc = Com::Attr::Stat::GroupCalc.new(
      method: 'group_sum',
      group_attrs: %w[id],
      group_dates: [
        { attr: 'created_at', by: 'day', options: { format: '%Y-%m-%d' } },
      ],
    )
    caculator_calc = Com::Attr::Stat::Caculation.new(
      caculations: [
        { name: 'test', attr: 'id', method: 'ta_pluck' },
      ],
    )

    User.ta_statistic(group_calc)

    group_item = Com::Attr::Stat::Item.new(
      key: 'group_item',
      caculator: group_calc,
    )
    caculator_item = Com::Attr::Stat::Item.new(
      key: 'caculator_item',
      caculator: caculator_calc,
    )
    User.ta_statistic(group_item)

    resource_stat = Com::Attr::Stat::Resource.new(
      refs: [
        {
          # relations: [:app, :users],
          item: caculator_item,
        },
      ],
    )
    User.first.ta_statistic(resource_stat)

    @forms_template.form = Forms::Attr::Item.new(
      column_attributes: [
        { key: 'id' },
        { key: 'name' },
        { key: 'ta_statistic.caculator_item' },
      ],
      resource_stat_condition: resource_stat,
    ).as_json

    # @folder = Com::Attr::Storage::Folder.new(name: 'folder1')
    # @folder.children = [
    #   Com::Attr::Storage::Folder.new(name: 'folder2'),
    #   Com::Attr::Storage::Folder.new(name: 'folder3'),
    #   Com::Attr::Storage::File.new(name: 'test.mp4', file_item: { path: '/Users/<USER>/test.mp4' } ),
    # ]

    # ZipTricks::Streamer.open('/Users/<USER>/test.zip') do |zip|
    #   folder.add_to_zip zip
    # end
    json = @forms_template.form_json(User.first, mode: 'detail')

    json = User.first.as_form_json(@forms_template)

    # model conf 配置
    create_list :model_conf, 2, model_define: @model_define
    @model_confs = ModelConf.all
    @model_conf = @model_confs.first
    @model_conf_count = @model_confs.count

    create_list :api_setting, 2, model_define: @model_define
    @api_settings = ApiSetting.all
    @api_setting = @api_settings.first
    @api_setting_count = @api_settings.count

    create_list :async_task, 2, app: @app, user: @user
    @async_tasks = AsyncTask.all
    @async_task = @async_tasks.first
    @async_task_count = @async_tasks.count
  end
end
