require 'ruby-pinyin'

module Com::Model::User
  extend ActiveSupport::Concern

  included do |base|
    include Auth::Authable

    self.track_migration = true

    formable

    # User 支持 role search
    ransackable_scope_defines.concat([:with_role_in, :with_role, :without_role, :without_role_in])

    rolify(
      after_add: ->(u, r) {
        u.touch
        u.try(:after_add_role, r)
      },
      after_remove: ->(u, r) {
        u.touch
        u.try(:after_remove_role, r)
      },
    )
    has_many :users_roles
    accepts_nested_attributes_for :users_roles, allow_destroy: true
    has_many :record_storages, class_name: 'Com::RecordStorage', dependent: :destroy
    has_many :async_tasks, class_name: 'AsyncTask', dependent: :destroy

    base.instance_eval do
      alias :with_role_in :with_role
      alias :without_role_in :without_role
    end

    effectable

    belongs_to :app
    belongs_to :tanent, optional: true
    belongs_to :ref_user, class_name: '::User', optional: true

    attribute :account, :string, comment: '账号，关联登录', index: true
    attribute :name, :string, comment: '用户姓名'
    attribute :nickname, :string, comment: '用户昵称'
    attribute :pinyin, :string, comment: '用户名拼音'
    attribute :mobile, :string, comment: '用户手机号'
    attribute :email, :string, comment: '用户邮箱'
    attribute :gender, :string, comment: '性别'
    attribute :avatar, :jsonb, comment: '用户头像'
    attribute :identity_id, :string, comment: '证件号码，需要时候可以作为唯一标识', index: true
    attribute :last_visit_at, :datetime, comment: '最后访问时间'
    attr_accessor :password_raw

    # 用户是否锁定
    has_event :block

    strip_attributes only: [:account, :name, :mobile, :email, :identity_id]

    validates_uniqueness_of :account, scope: :app_id, message: '该用户账号已注册，请确认'
    validates_presence_of :account

    before_save :set_pinyin_by_name, if: :will_save_change_to_name?

    def set_pinyin_by_name
      self.pinyin = PinYin.of_string(name).join('')
    end
  end

  def app_code
    app.code
  end

  def roles_label
    roles.pluck(:label)
  end

  def set_mod_roles(mod, *mod_roles_name)
    correct_mod_roles_name = mod.roles.pluck(:name)
    exist_roles_name = roles_name

    add_roles_name = (mod_roles_name - exist_roles_name) & correct_mod_roles_name
    remove_roles_name = (exist_roles_name - mod_roles_name) & correct_mod_roles_name

    add_roles_name.each do |role_name|
      add_role role_name
    end

    remove_roles_name.each do |role_name|
      remove_role role_name
    end
  end

  def avatar_storage_info
    user = self

    user.avatar['files']&.map do |file|
      file_name = [user.name, user.account, file['fileName']].compact.join('_')
      Com::Attr::Storage::File.new(name: file_name, file_item: file)
    end
  end

  class_methods do
    def auth_user_by_token!(token)
      account_info = auth!(token).with_indifferent_access
      account = account_info['account']
      app_id = account_info['app_id']
      app = App.find_by(code: app_id) || App.first
      app.users.effective.find_by(account: account)
    end

    def find_with_app_account(app_code:, account:)
      App.find_by!(
        code: app_code,
      ).users.find_by!(
        account: account,
      )
    end

    def current
      Current.user
    end
    alias_method :current_user, :current
  end
end
