module Com::Model::ModelConf
  extend ActiveSupport::Concern

  class Conf
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

    attr_json :reflections, Forms::Attr::ModelReflection.to_type, array: true
    attr_json :attrs, Forms::Attr::ModelAttribute.to_type, array: true
  end

  included do
    self.track_migration = true

    belongs_to :model_define, optional: true

    attribute :name, :string, comment: '名称'
    attribute :klass, :string, comment: '类名'
    attribute :conf, :jsonb, comment: '具体配置'

    serialize :conf, coder: Conf.to_serialization_coder

    default_value_for(:klass) { |o| o&.model_define&.klass }
    default_value_for(:name) { |o| o&.model_define&.name }

    def find_attribute(key)
      conf&.attrs&.find { |attribute| attribute.key == key }
    end
  end

  class_methods do
    # 如果系统数据库中存在相关的内容，则读取数据库的记录
    # 如果数据库中不存在，则从engine中的config/model_confs/#{klass}.json来获取
    def load_with_klass(klass)
      model_conf = find_by(klass: klass)
      return model_conf if model_conf.present?

      filename = "config/model_confs/#{klass}.json"
      json_path =
        if Rails.root.join(filename).exist?
          Rails.root.join(filename)
        else
          fit_engine = Rails::Engine.subclasses.find do |engine|
            engine.config.generators.api_only &&
              engine.to_s.start_with?('Rails') &&
              engine.root.join(filename).exist?
          end
          fit_engine.present? ? fit_engine.root.join(filename) : nil
        end
      return nil if json_path.blank?

      new(
        JSON.parse(File.read(json_path)),
      )
    end
  end
end
