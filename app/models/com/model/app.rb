module Com::Model::App
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    has_many :users, dependent: :destroy
    has_many :forms_templates, class_name: 'Forms::Template', dependent: :destroy
    has_many :themes, class_name: 'Com::Theme', dependent: :destroy
    has_many :search_items, class_name: 'Com::SearchItem', dependent: :destroy
    has_many :private_policies, class_name: 'Com::PrivatePolicy', dependent: :destroy
    has_one :private_policy, -> { order(position: :desc) }, class_name: 'Com::PrivatePolicy', dependent: :destroy
    has_many :model_settings, dependent: :destroy
    has_many :version_histories, class_name: 'Com::VersionHistory', dependent: :destroy
    has_many :page_settings, dependent: :destroy
    has_many :component_settings, dependent: :destroy
    has_many :tanents, dependent: :destroy
    has_many :data_forms, dependent: :destroy

    attribute :code, :string, comment: '应用标识'
    attribute :name, :string, comment: '应用的名称'
    attribute :settings, :jsonb, comment: '配置信息', default: {}

    validates_uniqueness_of :code
    validates_presence_of :code
  end

  # 这里返回所有的模型定义，包括系统预设和自己设置的settings
  def model_defines
    ModelDefine.all
  end

  def mods
    Mod.all
  end

  def roles
    Role.all
  end

  def env(key, default_value = nil)
    key = key.to_s
    settings&.dig(key) || ENV.fetch(key, default_value)
  end

  class_methods do
    def find(id_or_code)
      ci_where(:code, id_or_code)&.first || super(id_or_code)
    end
  end
end
