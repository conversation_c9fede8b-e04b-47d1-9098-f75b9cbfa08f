module Com::Model::ModelSetting
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :model_define
    belongs_to :setable, polymorphic: true, optional: true
    belongs_to :app, optional: true
    belongs_to :forms_template, class_name: 'Forms::Template', foreign_key: :forms_template_id, optional: true

    attribute :flag, :string, default: 'model', comment: '同一个模型中的不同定义，其中model代表是这个对象的模型'
    attribute :flag_name, :string, default: '模型定义', comment: 'flag对应中文名称'
    attribute :form, :jsonb, comment: '可以直接定义表单'
    serialize :form, coder: Forms::Attr::Item.to_serialization_coder
    attribute :form_setting, :jsonb, comment: '表单结构'
    serialize :form_setting, coder: Forms::Attr::FormSetting.to_serialization_coder
    attribute :api_config, :jsonb, comment: 'API Config'
    serialize :api_config, coder: Forms::Attr::ApiConfig.to_serialization_coder

    nilify_blanks only: [:flag, :setable_type, :setable_id]

    validates_uniqueness_of :flag, scope: [:model_define, :setable_type, :setable_id, :app_id]

    before_create :set_model_form
    strip_attributes only: [:setable_type]
  end

  def fake_template
    Forms::Template.new(
      # form: form&.as_json,
      id: id,
      form: form_setting&.confs&.first&.form&.as_json || form&.as_json,
      app: app,
      uuid: "#{model_define.klass}##{flag}",
      name: "#{model_define.name}#{flag_name}",
    )
  end

  def i18n_form_setting
    if defined?(Trans)
      Trans::Record.where(source_id: id, source_type: 'Forms::Template').each_with_object({}) do |record, result|
        val = record.payload&.dig('form')
        form_setting = Forms::Attr::FormSetting.init
        form_setting.confs.push(
         Forms::Attr::FormConf.new(
            conf: Forms::Attr::Conf::Copy.new(form: val)
          )
        )
        result[record.lang] = val ? form_setting : nil
      end
    else
      {}
    end
  end

  def i18n_form_setting=(val)
    if defined?(Trans)
      val&.each_pair do |key, value|
        Trans::Record.find_or_initialize_by(
          source_type: 'Forms::Template',
          source_id: id,
          lang: key,
        ).update!(
          payload: { form: Forms::Attr::FormSetting.new(value).confs.first&.form }
        )
      end
    end
  end

  def model_class
    model_define.model_class
  end

  def set_model_form
    return if form.present?

    self.form = model_define.default_form
  end

  define_method(:form) do
    super() || forms_template&.form
  end

  class_methods do
    def template_by_singular_name(model_singular_name, flag: 'model')
      filename = "config/model_settings/#{model_singular_name}##{flag}.json"
      json_path =
        if Rails.root.join(filename).exist?
          Rails.root.join(filename)
        else
          fit_engine = Rails::Engine.subclasses.find do |engine|
            engine.config.generators.api_only &&
              engine.to_s.start_with?('Rails') &&
              engine.root.join(filename).exist?
          end
          fit_engine.present? ? fit_engine.root.join(filename) : nil
        end
      return nil if json_path.blank?

      Forms::Template.new(
        form: JSON.parse(File.read(json_path)),
        app: nil,
        uuid: "#{model_singular_name}##{flag}",
        name: "#{model_singular_name}##{flag}",
      )
    end
  end
end
