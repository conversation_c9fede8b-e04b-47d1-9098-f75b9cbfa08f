module Com::Model::PrivatePolicy
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    acts_as_list scope: [:app_id]

    belongs_to :app

    attribute :name, :string, comment: '条款名称'
    attribute :key, :string, comment: '关键字，可能有不同业务模块需要使用的关键字'
    attribute :content, :jsonb, comment: '隐私条款内容'
    attribute :position, :integer, comment: '排序'
  end

  class_methods do
  end
end
