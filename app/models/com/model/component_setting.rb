module Com::Model::ComponentSetting
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    seqable
    formable

    belongs_to :app, optional: true

    attribute :name, :string, comment: '组件配置名称'
    attribute :component_klass, :string, comment: '组件类名称'
    attribute :component_path, :string, comment: '组件类路径'
    attribute :conf, :jsonb, comment: '组件配置的json结构'
  end

  class_methods do
  end
end
