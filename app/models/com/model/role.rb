module Com::Model::Role
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    has_and_belongs_to_many :users, join_table: :users_roles

    belongs_to :resource, polymorphic: true, optional: true
    belongs_to :mod, optional: true

    attribute :name, :string, comment: '权限标识', index: true
    attribute :label, :string, comment: '显示名称', index: true

    validates :resource_type,
              inclusion: { in: Rolify.resource_types },
              allow_nil: true

    scopify
  end

  class_methods do
  end
end

