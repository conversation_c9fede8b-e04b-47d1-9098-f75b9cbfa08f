# 数据表单，包含了表单信息和数据信息
# 表单信息：可以包含引用表单、复制表单两种方式，其中引用表单，使用的还是model_setting的方式
# 数据信息：保存在payload中，数据需要增加
module Com::Model::DataForm
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    stiable
    formable

    attr_accessor :payload_cache

    belongs_to :app
    # 重建这个数据表单的User用户
    belongs_to :create_user, class_name: '::User', optional: true
    # 数据表单对应源头对象，可以是ActiveRecord / Plan::Task / Bpm::Instance
    belongs_to :source, polymorphic: true, optional: true
    # 关联的数据
    belongs_to :record, polymorphic: true, optional: true

    attribute :flag, :string, comment: '可用作同一 source 下不同的关联关系的区分'
    attribute :source_flag, :string, comment: '关联source的flag'
    attribute :state, :string, comment: '数据状态'
    attribute :payload, :jsonb, comment: '存储的信息', default: {}
    attribute :summary, :jsonb, comment: '通过form生成的缩略信息'
    attribute :form_conf, :jsonb, comment: '表单的配置，里面支持多态的方式'
    serialize :form_conf, coder: Forms::Attr::FormConf.to_serialization_coder
    attribute :options, :jsonb, comment: '额外的数据信息'
    attribute :meta, :jsonb, comment: '预留后续的数据存储'
    attribute :form_conf_seq, :string, comment: '表单配置的seq，方便进行检索'

    default_value_for(:app) { |o| o.create_user&.app || o.source.try(:app) }
    default_value_for(:form_conf_seq) { |o| o.form_conf&.seq }

    alias_attribute :user, :create_user

    # enum state: {
    #   created: 'created',
    #   processing: 'processing',
    #   completed: 'completed',
    #   terminated: 'terminated',

    #   rejected: 'rejected',
    #   failed: 'failed',
    #   been_reject: 'been_reject',
    #   been_fail: 'been_fail',
    #   inactivated: 'inactivated'
    # }, _suffix: true

    def payload=(attributes)
      self.payload_cache = attributes

      super(attributes)
    end

    def form(*args)
    end

    attr_accessor :form_setting_cache

    def invoke
    end
  end

  class_methods do
  end
end
