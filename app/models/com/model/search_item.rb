module Com::Model::SearchItem
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    acts_as_list scope: [:app_id]

    belongs_to :app

    attribute :name, :string, comment: '搜索条件 '
    attribute :position, :integer, comment: '位置'
    attribute :group_name, :string, comment: '分组标识'
    attribute :enabled, :boolean, comment: '是否启用'
    attribute :conditions, :jsonb, comment: '具体ransack搜索条件'
    serialize :conditions, coder: Com::Attr::Search::Condition.to_serialization_coder

    default_value_for(:enabled) { true }
  end

  class_methods do
  end
end
