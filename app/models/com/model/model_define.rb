module Com::Model::ModelDefine
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    has_many :model_settings, dependent: :destroy
    has_one :model_conf, dependent: :nullify
    has_many :api_settings, dependent: :nullify

    attribute :klass, :string, comment: '对应设置的Model名称'
    attribute :name, :string, comment: '模型设置的中文名'
    attribute :association_chain, :string, array: true, comment: '查找的关系列表'
    attribute :klass_singular, :string, comment: '自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找'

    validates_uniqueness_of :klass
    before_save :set_klass_singular!
  end

  def model_setting_by_setable(setable, app: setable.try(:app), flag: 'model', setable_type: nil)
    # 先查找自己的，再通过association_chain进行查找
    # 再查找App相关的
    # 最后再查找setable为nil的情况（全局）
    if setable.present?
      # 首先查找完全配置的是setable的内容
      model_settings.find_by(setable: setable, flag: flag) ||
        # 根据配置的association查找对应的内容
        model_setting_by_setable_with_association_chain(setable, flag: flag) ||
        # 查找app下配置的内容
        model_settings.find_by(setable: nil, app: app, flag: flag, setable_type: setable_type) ||
        model_settings.find_by(setable: app, flag: flag) ||
        model_settings.find_by(setable: nil, flag: flag, setable_type: setable_type) ||
        model_settings.find_by(setable: nil, flag: flag)
    else
      model_settings.find_by(setable: nil, app: app, flag: flag, setable_type: setable_type) ||
        model_settings.find_by(setable: app, flag: flag) ||
        model_settings.find_by(setable: nil, flag: flag, setable_type: setable_type)
    end
  end

  def model_setting_by_setable_with_association_chain(setable, flag: nil)
    association_setable = setable

    # 遍历关联链，查找对应的设置
    (association_chain || []).reverse_each do |association|
      association_setable = association_setable.try(association)
      return nil if association_setable.blank?

      # 批量查询设置，避免多次数据库查询
      _setting = model_settings.find_by(setable: association_setable, flag: flag)
      return _setting if _setting.present?
    end

    nil
  end

  def model_class
    klass.camelize.constantize
  end

  def default_form
    return nil unless model_class.respond_to?(:attributes_to_define_after_schema_loads)

    attributes = model_class.attributes_to_define_after_schema_loads
    Forms::Attr::Item.new(
      type: 'layout',
      model: {},
      model_key: "layout_#{rand(1..100_000_000_000)}",
      options: { label: {} },
      fields: attributes.map { |attribute_key, attribute_define| Forms::Attr::Item.create_by_attribute(attribute_key, attribute_define) }.compact,
    )
  end

  # 从本地配置的文件里获取
  def template_form_by_flag(flag: 'model', setable: nil)
    model_setting_by_setable(setable, flag: flag)&.form ||
      ModelSetting.template_by_singular_name(
        klass_singular,
        flag: flag,
      )&.form
  end

  private

  def set_klass_singular!
    self.klass_singular = klass.constantize.model_name.singular
  end

  class_methods do
    def class_by_singular_name(klass_singular)
      find_by!(klass_singular: klass_singular.singularize).klass.constantize
    end

    def model_setting_by_class(klass, app:, flag: 'model')
      model_setting_by_singular_name(klass.model_name.singular, flag: flag, app: app) ||
        model_setting_by_singular_name(klass.base_class.model_name.singular, flag: flag, app: app)
    end

    def model_setting_by_singular_name(klass_singular, app: nil, flag: 'model', setable_type: nil, setable_id: nil)
      return nil unless klass_singular.present?
      setable = setable_type.present? && setable_id.present? ? setable_type.constantize.find(setable_id) : nil
      find_by(klass_singular: klass_singular.singularize)&.model_setting_by_setable(
        setable, app: app, flag: flag || 'model', setable_type: setable_type,
      )
    end
  end
end
