module Com::Model::ApiSetting
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :model_define, optional: true
    belongs_to :app, optional: true

    attribute :klass, :string, comment: '对应的active record class name'
    attribute :action, :string, comment: '对应controller的action'
    attribute :uid, :string, comment: '自动生成的唯一标识', index: true
    attribute :extract_conf, :jsonb, comment: '数据抽取配置'
    serialize :extract_conf, coder: Forms::Attr::ExtractConf.to_serialization_coder

    default_value_for(:uid) { SecureRandom.hex(10) }
    default_value_for(:klass) { |o| o&.model_define&.klass }
  end

  class_methods do
  end
end
