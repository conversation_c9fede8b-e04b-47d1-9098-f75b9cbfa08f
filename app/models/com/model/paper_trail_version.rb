module Com::Model::PaperTrailVersion
  extend ActiveSupport::Concern

  included do
    include PaperTrail::VersionConcern

    self.track_migration = true

    belongs_to :operator, polymorphic: true, optional: true

    attribute :item_type, :string, null: false
    attribute :item_id, :integer, null: false
    attribute :event, :string, null: false, comment: 'create, update, destroy'
    attribute :whodunnit, :string, comment: 'whodunnit'
    attribute :object, :jsonb, comment: 'object attributes'
    attribute :object_changes, :jsonb, comment: 'object changes'
    attribute :controller_info, :jsonb, comment: 'controller info'
    attribute :model_info, :jsonb, comment: 'model info'

    index [:item_type, :item_id], name: 'index_versions_on_item_id_item_type'

    after_create :create_version_relations
    after_destroy :destroy_version_relations

    def create_version_relations
      item_klass = item_type.constantize
      return unless item_klass.respond_to?(:version_relation_class_name)
      return unless item_klass.version_relation_class_name

      version_relation_klass = item_klass.version_relation_class_name.constantize

      item_klass.version_belongs.each do |belong|
        if belong.is_a?(Hash)
          belong.each do |_belong, condition|
            if condition.respond_to?(:call) && condition.call(self)
              version_relation_klass.create(
                app: operator&.app,
                resource: item.send(_belong.to_sym),
                real_resource: item,
                version: self,
                operator: operator,
              )
            end
          end
        else
          version_relation_klass.create(
            app: operator&.app,
            resource: item.send(belong),
            real_resource: item,
            version: self,
            operator: operator,
          )
        end
      end
    end

    def destroy_version_relations
      item_klass = item_type.constantize
      return unless item_klass.respond_to?(:version_relation_class_name)
      return unless item_klass.version_relation_class_name

      version_relation_klass = item_klass.version_relation_class_name.constantize
      version_relation_klass.where(version: self).destroy_all
    end


    def human_info
      {
        operator: operator&.name,
        item_type: I18n.t_model_name(item_type),
        event: I18n.t_action_name(event),
      }
    end

  end

  class_methods do
  end
end