module Com::Model::AsyncTask
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    seqable
    stiable

    belongs_to :app, optional: true
    belongs_to :user, class_name: '::User', optional: true
    belongs_to :taskable, polymorphic: true, optional: true

    attribute :flag, :string, comment: '程序使用参数，唯一标识，前端配合使用'
    attribute :name, :string, comment: '任务名称'
    attribute :progress, :integer, comment: '进度(取整数)'
    attribute :state, :string, comment: '任务状态'
    attribute :perform_args, :string, comment: '执行参数'
    attribute :options, :jsonb, comment: '启动执行参数'
    attribute :payload, :jsonb, comment: '处理信息'
    attribute :result, :jsonb, comment: '异步处理的结果信息'
    attribute :meta, :jsonb, comment: '额外信息'

    enum state: {
      pending: 'pending',
      processing: 'processing',
      completed: 'completed',
      failed: 'failed',
      terminated: 'terminated',
    }

    default_value_for(:app) { |o| o.user&.app }
    default_value_for(:state) { 'pending' }

    after_create :reset_name!

    def reset_name!
      update_columns(name: "后台任务_#{seq}") if name.blank?
    end

    # def set_progress!(progress:)
    #   self.state = 'completed' if progress == 100
    #   self.progress = progress
    #   self.save!
    # end
    #
    # def set_result!(state: 'completed', result: {})
    #   self.state = state
    #   self.result = result
    #   self.save!
    # end

    def perform_later
      taskable.try(:perform_later)
    end
  end

  class_methods do
  end
end
