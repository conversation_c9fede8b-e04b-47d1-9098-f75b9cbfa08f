module Com::Model::Tan<PERSON>
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app

    attribute :code, :string, comment: '租户标识'
    attribute :name, :string, comment: '租户名称'

    validates_uniqueness_of :code
    validates_presence_of :code
  end

  class_methods do
    def current
      Current.tanent
    end

    alias current_tanent current
    alias current_tenant current
  end
end