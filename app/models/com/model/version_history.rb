module Com::Model::VersionHistory
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    acts_as_list scope: [:app_id]

    belongs_to :app
    belongs_to :creator, optional: true, class_name: '::User'

    attribute :name, :string, comment: '版本发布名称'
    attribute :version, :string, comment: '版本号'
    attribute :content, :jsonb, comment: '发布说明'
    attribute :position, :integer, comment: '发布顺序'

  end

  class_methods do
  end
end
