# frozen_string_literal: true

module Com
  module Invokers
    ##
    # Literal invoker which allows to use strings or symbols to call
    # record methods as state/event/transition callbacks.
    class LiteralInvoker < BaseInvoker
      def may_invoke?
        subject.is_a?(String) || subject.is_a?(Symbol)
      end

      def log_failure
        failures << subject
      end

      def invoke_subject
        @result = exec_subject
      end

      private

      def subject_arity
        @arity ||= record.__send__(:method, subject.to_sym).arity
      end

      def subject_parameters
        @parameters ||= record.__send__(:method, subject.to_sym).parameters
      end

      # rubocop:disable Metrics/AbcSize
      def exec_subject
        raise(*record_error) unless record.respond_to?(subject, true)
        return record.__send__(subject) if subject_arity.zero?

        if subject_arity < 0
          if args.is_a?(Hash)
            return record.__send__(subject, **args.symbolize_keys)
          elsif args.is_a?(Array)
            return record.__send__(subject, *args)
          else
            return record.__send__(subject, args)
          end
        end

        arg_arity = subject_parameters.select { |p| !p.first.to_s.start_with?('key') }.count
        key_arity = subject_arity - arg_arity

        _args = if arg_arity > 0
                  args.is_a?(Array) ? args[0..(arg_arity - 1)] : [args][0..(arg_arity - 1)]
                else
                  []
                end
        _keys = if key_arity > 0
                  args.is_a?(Array) ? args[arg_arity..(subject_arity - 1)].to_h : args
                else
                  {}
                end

        record.__send__(subject, *_args, **_keys.symbolize_keys)

        # record.__send__(subject, *args[0..(subject_arity - 1)])
      end
      # rubocop:enable Metrics/AbcSize

      def record_error
        [
          NoMethodError,
          'NoMethodError: undefined method ' \
          "`#{subject}' for #{record.inspect}:#{record.class}"
        ]
      end
    end
  end
end
