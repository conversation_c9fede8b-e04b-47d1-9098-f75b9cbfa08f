class Com::CounterStorage
  def initialize(attrs, records)
    @attrs = Array(attrs)
    @records = Array(records)
  end

  ## Example
  # s = Com::CounterStorage.load([:user_type, :user_id, :state], Plan::Token.where(user_type: 'Org').group(:user_type, :user_id, :state).count)

  # keys 是二维数组
  # values是计数值
  def self.load(attrs, counter_pairs = {}, enum_dics: {})
    attrs = Array.wrap(attrs)
    records = counter_pairs.map do |key_array, value|
      record = OpenStruct.new(Array(attrs).zip(Array(key_array)).to_h)
      record._localvalue = value
      # 转换枚举值
      enum_dics.each do |attr, enum_dic|
        record[attr] = enum_dic.fetch(record[attr], record[attr])
      end
      record
    end.sort { |a, b| a._localvalue <=> b._localvalue }
    new(attrs, records)
  end

  def where(**query)
    self.class.new(
      @attrs,
      @records.filter do |record|
        query.all? do |k, v|
          v.is_a?(Array) ?
            v.any? { |val| record.send(k) == val } :
            record.send(k) == v
        end
      end,
    )
  end

  def not(**query)
    self.class.new(
      @attrs,
      @records.reject do |record|
        query.all? do |k, v|
          v.is_a?(Array) ?
            v.any? { |val| record.send(k) == val } :
            record.send(k) == v
        end
      end,
    )
  end

  def count
    @records.count
  end

  def sum
    @records.reduce(0) { |_sum, _record| _sum += _record._localvalue }
  end

  def group(*attrs, include_key: false)
    @records.map do |record|
      if attrs.size == 1
        include_key ? { attrs.first => record.send(attrs.first) } : record.send(attrs.first)
      else
        include_key ?
          attrs.each_with_object({}) { |attr, info| info[attr] = record.send(attr) } :
          attrs.map { |attr| record.send(attr) }
      end
    end.uniq
  end

  def group_count(*attrs, include_count: false, include_key: false)
    return {} if attrs.count == 0

    init_value = include_count ? { all_count: count } : {}
    attr = attrs.first
    group_attr = group(*attr, include_key: include_key)
    if attrs.count == 1
      group_attr.each_with_object(init_value) do |val, info|
        where_query = Array(attr).zip(Array(val)).to_h
        info[val] = where(**where_query).count
      end
    else
      other_attrs = attrs.slice(1, attrs.length - 1)
      group_attr.each_with_object(init_value) do |val, info|
        where_query = Array(attr).zip(Array(val)).to_h
        info[val] = where(**where_query).group_count(*other_attrs, include_count: include_count, include_key: include_key)
      end
    end
  end

  def group_sum(*attrs, include_sum: false, include_key: false)
    return {} if attrs.count == 0

    init_value = include_sum ? { all_count: sum } : {}
    attr = attrs.first
    group_attr = group(*attr, include_key: include_key)
    if attrs.count == 1
      group_attr.each_with_object(init_value) do |val, info|
        where_query = Array(attr).zip(Array(val)).to_h
        info[val] = where(**where_query).sum
      end
    else
      other_attrs = attrs.slice(1, attrs.length - 1)
      group_attr.each_with_object(init_value) do |val, info|
        where_query = Array(attr).zip(Array(val)).to_h
        info[val] = where(**where_query).group_sum(*other_attrs, include_sum: include_sum, include_key: include_key)
      end
    end
  end
end
