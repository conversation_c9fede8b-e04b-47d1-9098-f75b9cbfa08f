require 'closure_tree'

module Com::Ext::ClosureTree
  extend ActiveSupport::Concern

  included do
    attribute :parent_id, :integer, comment: 'closure tree parent_id'

    def children_count
      children.count
    end
  end

  class_methods do
    alias_method(:origin_to_node, :to_node) if method_defined?(:to_node)
    alias_method(:origin_unscoped_where, :unscoped_where) if method_defined?(:unscoped_where)
    alias_method(:origin_roots, :roots) if method_defined?(:roots)
    alias_method(:origin_ancestors_of, :ancestors_of) if method_defined?(:ancestors_of)
    alias_method(:origin_children_of, :children_of) if method_defined?(:children_of)
    alias_method(:origin_subtree_of, :subtree_of) if method_defined?(:subtree_of)
    alias_method(:origin_siblings_of, :siblings_of) if method_defined?(:siblings_of)

    def to_node(object)
      if object.is_a?(base_class)
        object
      else
        unscoped_where { |scope| scope.find(object.try(primary_key) || object) }
      end
    end

    def unscoped_where
      yield base_class.unscope(:where)
    end

    def ancestors_of(object)
      node = to_node(object)
      node.ancestors
    end

    def children_of(object)
      node = to_node(object)
      node.children
    end

    def subtree_of(object)
      node = to_node(object)
      node.self_and_descendants
    end

    def descendants_of(object)
      node = to_node(object)
      node.descendants
    end

    def siblings_of(object)
      node = to_node(object)
      node.siblings
    end
  end
end
