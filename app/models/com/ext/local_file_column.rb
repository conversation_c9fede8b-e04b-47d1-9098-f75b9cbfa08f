module Com::Ext::LocalFileColumn
  extend ActiveSupport::Concern

  included do
  end

  class_methods do
    def local_file_columns *columns, **opts
      columns.each do |column|
        define_local_column(column, **opts)
      end
    end

    private

    def define_local_column column, **opts
      class_eval do
        define_method("#{column}_instance") do
          variable = "@#{column}_instance"
          instance_variable_set(variable, LocalFileColumn.new(self, column, **opts)) unless instance_variable_get(variable)
          instance_variable_get(variable)
        end

        define_method("#{column}=") do |val|
          try("#{column}_instance").assign(val)
        end

        define_method("#{column}") do
          try("#{column}_instance").exist? ? try("#{column}_instance").url : nil
        end

        define_method("#{column}_full_path") do
          try("#{column}_instance").exist? ? try("#{column}_instance").full_path : nil
        end

        define_method("clear_#{column}") do
          try("#{column}_instance").clear
        end

        after_destroy :"clear_#{column}"
      end
    end
  end

  class LocalFileColumn
    attr_accessor :object, :column, :dir

    def initialize object, column, **opts
      @object = object
      @dir = File.join(Rails.root, 'public', column.to_s)
    end

    def filename
      "#{object.id}"
    end

    def full_path
      File.join(dir, filename)
    end

    def url
      ActionController::Base.helpers.asset_url(full_path.gsub(/^.+?\/public/, ''))
    end

    def exist?
      File.exist?(full_path)
    end

    def clear
      FileUtils.rm(full_path) if exist?
    end

    def assign io
      clear
      FileUtils.mkdir_p(dir) unless File.exist?(dir)
      f = File.new(full_path, 'w+')
      f.write(io)
      f.close
    end
  end
end
