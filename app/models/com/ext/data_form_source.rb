module Com::Ext::DataFormSource
  extend ActiveSupport::Concern

  FORM_SETTING_METHOD_REGEXP = /data_form_(\w+)_payload/.freeze

  included do
    class_attribute :form_setting_block_map, default: {}
    class_attribute :data_form_association_names, default: []
    attr_accessor :data_forms_attributes_cache_map

    def collect_form_settings data_form_association_name
      data_form_association_name = data_form_association_name.to_s

      origin_form_setting = self.class.form_setting_block_map[data_form_association_name].call(self) || Forms::Attr::FormSetting.init
      data_form_specify_form_confs = send(data_form_association_name).where(
        type: ["DataFormRecordCreate", "DataFormCreateRecord", "DataFormUpdateRecord", "DataFormDestroyRecord"]
      ).map(&:form_conf).compact

      origin_form_setting.merge_form_confs(data_form_specify_form_confs)
    end

    def form_settings
      @form_settings ||= self.class.data_form_association_names.map do |data_form_association_name|
        collect_form_settings(data_form_association_name)
      end.reduce do |out, form_setting|
        out.merge_form_confs(form_setting.confs)
      end
    end

    def relate_date_forms
      data_form_association_names.reduce([]) do |out, method_name|
        out.concat(send(method_name))
      end
    end
  end

  class_methods do
    def form_setting_with(
      data_form_association_name: 'data_forms',
      data_form_class_name: 'DataForm',
      flag: nil,
      &block
    )
      data_form_association_name = data_form_association_name.to_s

      self.data_form_association_names << data_form_association_name

      define_data_forms_association(
        data_form_association_name: data_form_association_name,
        data_form_class_name: data_form_class_name,
        flag: flag,
      )

      self.form_setting_block_map[data_form_association_name.to_s] = block

      payload_method_name = "#{data_form_association_name.singularize}_payload"
      form_setting_method_name = "#{data_form_association_name.singularize}_form_setting"

      # permit 中写 data_form_payload: {}
      # 当 form_setting 的 seq 中，有对应的 seq 的时候，创建或更新 data_form
      # data_form_payload: { data_form_#{seq}_payload { xxx: xxx } }

      class_eval <<-EVAL, __FILE__, __LINE__ + 1
        before_validation :trigger_all_data_form

        def trigger_all_data_form
          # 触发创建所有的 data_form
          if self.#{payload_method_name}.keys.length == 0 && self.data_forms_attributes_cache_map&.[]("#{data_form_association_name}").nil?
            self.#{payload_method_name} = {}
          end
        end

        define_method("#{payload_method_name}=") do |val|
          form_setting = #{form_setting_method_name}

          form_setting.confs.map do |form_conf|
            # form_conf_seq = FORM_SETTING_METHOD_REGEXP.match(name.to_s).captures[0]
            # next unless form_conf_seq
            form_conf_seq = form_conf.seq
            name = "data_form_\#{form_conf_seq}_payload"

            form_setting.create_or_update_data_form_by_seq(
              seq: form_conf_seq,
              source: self,
              find_attributes: {
                form_conf_seq: form_conf_seq,
              },
              extra_attributes: { payload: val&.stringify_keys&.[](name) || {} },
              data_form_association_name: "#{data_form_association_name}"
            )
          end
        end

        define_method("#{payload_method_name}") do
          #{data_form_association_name}.reduce({}) do |out, data_form|
            out.merge(
              "data_form_\#{data_form.form_conf_seq}_payload" => data_form.payload,
            )
          end
        end

        define_method("#{form_setting_method_name}") do
          collect_form_settings("#{data_form_association_name}")
        end
      EVAL
    end

    # replace_accessors 例如 payload，cache_payload
    # 按数组顺序对应 form setting 中的 confs
    # NOTE: 不要直接用 data_forms_attributes

    def form_setting_accessor(
      *replace_accessors,
      data_form_association_name: 'data_forms'
    )
      form_setting_method_name = "#{data_form_association_name.singularize}_form_setting"

      class_eval <<-EVAL, __FILE__, __LINE__ + 1
        replace_accessors.each_with_index do |name, index|
          define_method(name) do
            form_setting = #{form_setting_method_name}
            return nil unless form_setting&.confs.present?

            form_conf_seq = form_setting.confs[index]&.seq
            #{data_form_association_name}.find_by(form_conf_seq: form_conf_seq)&.payload || {}
          end

          define_method("\#{name}=") do |val|
            ::Rails.logger.info "data_form_source: \#{name}=\#{val}"
            form_setting = #{form_setting_method_name}
            return unless form_setting&.confs&.[](index).present? && val.present?

            update_#{data_form_association_name}_by_form_conf(
              form_setting.confs[index],
              { form_conf_seq: form_setting.confs[index].seq },
              { payload: val }
            )
          end
        end
      EVAL
    end

    private

    def define_data_forms_association(
      data_form_association_name: 'data_forms',
      data_form_class_name: 'DataForm',
      flag: nil
    )
      class_eval <<-EVAL, __FILE__, __LINE__ + 1
        has_many(:#{data_form_association_name}, ->{ #{flag ? "where(flag: \"#{flag}\")" : ''} }, class_name: "#{data_form_class_name}", as: :source, dependent: :destroy)
        accepts_nested_attributes_for(:#{data_form_association_name}, reject_if: :all_blank, allow_destroy: true)

        before_save do
          self.data_forms_attributes_cache_map ||= {}
          if self.data_forms_attributes_cache_map["#{data_form_association_name}"]
            self.#{data_form_association_name}_attributes = data_forms_attributes_cache_map["#{data_form_association_name}"]
            data_forms_attributes_cache_map.delete("#{data_form_association_name}")
          end
        end

        def update_#{data_form_association_name}_by_form_conf(form_conf, find_attributes, extra_attributes = {})
          self.data_forms_attributes_cache_map ||= {}
          if new_record?
            self.data_forms_attributes_cache_map["#{data_form_association_name}"] ||= []
            self.data_forms_attributes_cache_map["#{data_form_association_name}"].reject! do |i|
              i.symbolize_keys[:form_conf_seq] == form_conf.seq
            end

            self.data_forms_attributes_cache_map["#{data_form_association_name}"].push({
                                                                                  form_conf_seq: form_conf.seq,
                                                                                  form_conf: form_conf,
                                                                                  type: form_conf.data_form_type,
                                                                                  source: self,
                                                                                  **extra_attributes,
                                                                                })
          else
            #{data_form_association_name}.find_or_initialize_by(
              # type: form_conf.data_form_type == '#{data_form_class_name}' ? nil : form_conf.data_form_type,
              **find_attributes,
            ).update!(
              form_conf: form_conf,
              **extra_attributes,
            )
          end
        end
      EVAL
    end
  end
end
