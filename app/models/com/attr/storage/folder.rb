class Com::Attr::Storage::Folder
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :name, :string
  attr_json :ftype, :string, default: 'folder'
  attr_json :children, AttrJson::Type::PolymorphicModel.new(
    Com::Attr::Storage::Folder,
    Com::Attr::Storage::File,
  ), array: true

  def add_to_zip zip, path: nil
    folder_name = File.join [path, name].compact
    zip.add_empty_directory(dirname: folder_name)

    if children.present?
      children.each do |child|
        child&.add_to_zip zip, path: folder_name
      end
    end
  end
end
