require 'open-uri'

class Com::Attr::Storage::File
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :name, :string
  attr_json :ftype, :string, default: 'file'
  attr_json :file_item, ActiveModel::Type::Value.new

  # 获取S3 URL
  def fetch_url_from_s3
    if file_item['url'].present?
      ENV['S3_SERVICE'] ? file_item['url'] : File.join(ENV['S3_ENDPOINT'], ENV['S3_BUCKET'], CGI.escape(file_item['key'] || file_item['fileKey']))
    else
      File.join(ENV['S3_ENDPOINT'], ENV['S3_BUCKET'], file_item['downloadPath'])
    end
  end

  # 从URL获取文件内容
  def fetch_file_content(url)
    URI.open(url).read
  end

  # 获取本地文件内容
  def fetch_local_file_content
    file_local_path = File.join('public', file_item['downloadPath'])
    File.open(file_local_path, 'rb', &:read)
  end

  # 写入文件到ZIP
  def write_to_zip(zip, file_name, content)
    zip.write_deflated_file(file_name) do |sink|
      sink << content
    end
  end

  # 处理文件内容并写入ZIP
  def process_file(zip, file_name)
    if ENV['S3_BUCKET'].present?
      url = fetch_url_from_s3
      file_content = fetch_file_content(url)
    else
      file_content = fetch_local_file_content
    end

    write_to_zip(zip, file_name, file_content)
  end

  # 添加文件到ZIP
  def add_to_zip(zip, path: nil)
    return unless file_item.present?

    file_name = File.join([path, name].compact)

    process_file(zip, file_name)
  rescue StandardError => e
    Rails.logger.error(e.inspect)
  end
end
