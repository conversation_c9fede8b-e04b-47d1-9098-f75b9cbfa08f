class Com::Attr::ConditionRules::String
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :opt, :string
  # validates :opt, inclusion_in: ['==', '!=', 'contains']
  attr_json :key, :string
  attr_json :key_name, :string
  attr_json :val, :string

  def valid? payload
    rule_value = Com::Attr::Condition.rule_value(payload, key)

    case opt
    when '=='
      rule_value.to_s == val
    when '!='
      rule_value.to_s != val
    when 'contains'
      rule_value.to_s.include? val
    else
      false
    end
  end
end
