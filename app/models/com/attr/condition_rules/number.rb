class Com::Attr::ConditionRules::Number
  include ActiveModel::Validations
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :opt, :string
  # validates :opt, inclusion_in: ['>', '>=', '<', '<=', '==', '!=', 'between']
  attr_json :key, :string
  attr_json :key_name, :string
  attr_json :val, ActiveModel::Type::Value.new

  def valid? payload
    rule_value = Com::Attr::Condition.rule_value(payload, key)

    case opt
    when 'between'
      rule_value.to_d.between?(val[0].to_d, val[1].to_d)
    else
      begin
        eval("#{rule_value} #{opt} #{val}")
      rescue Exception => ex
        false
      end
    end
  end
end
