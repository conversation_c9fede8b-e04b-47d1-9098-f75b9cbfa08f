class Com::Attr::ConditionRules::Boolean
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :opt, :string
  # validates :opt, inclusion_in: ['==', '!=']
  attr_json :key, :string
  attr_json :key_name, :string
  attr_json :val, :boolean

  def valid? payload
    rule_value = Com::Attr::Condition.rule_value(payload, key)

    begin
      eval("#{rule_value} #{opt} #{val}")
    rescue Exception => ex
      false
    end
  end
end
