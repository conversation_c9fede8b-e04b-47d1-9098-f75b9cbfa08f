class Com::Attr::ConditionRules::SingleChoice
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :opt, :string
  # validates :opt, inclusion_in: ['include' 'none']
  attr_json :key, :string
  attr_json :key_name, :string
  attr_json :val, ActiveModel::Type::Value.new, array: true

  def valid? payload
    rule_value = Array(Com::Attr::Condition.rule_value(payload, key))

    case opt
    when 'include'
      (val & rule_value).present?
    when 'none'
      (val & rule_value).blank?
    else
      false
    end
  end
end
