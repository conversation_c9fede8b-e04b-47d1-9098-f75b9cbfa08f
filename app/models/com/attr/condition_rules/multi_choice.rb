class Com::Attr::ConditionRules::MultiChoice
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :opt, :string
  # validates :opt, inclusion_in: ['include' 'none', 'any', '==']
  attr_json :key, :string
  attr_json :key_name, :string
  attr_json :val, ActiveModel::Type::Value.new, array: true

  def valid? payload
    rule_value = Array(Com::Attr::Condition.rule_value(payload, key)).to_set
    val_set = val.to_set

    case opt
    when 'include'
      rule_value.subset? val_set
    when 'none'
      (val_set & rule_value).blank?
    when 'any'
      (val_set & rule_value).present?
    when '=='
      val_set == rule_value
    else
      false
    end
  end
end
