class Com::Attr::LimitCountOption
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :opt, :string, default: 'none'
  # validates :opt, inclusion_in: ['>=', '<=', '==', 'none']
  attr_json :val, :integer

  # need 还需要多少，nil代表无限制
  # rest 还剩余多少, nil代表无限制
  def info(count)
    count = count.to_i

    case opt
    when 'none'
      { count: count, need: nil, rest: nil }
    when '>='
      { count: count, need: [val - count, 0].max, rest: nil }
    when '=='
      { count: count, need: [val - count, 0].max, rest: [val - count, 0].max }
    end
  end
end
