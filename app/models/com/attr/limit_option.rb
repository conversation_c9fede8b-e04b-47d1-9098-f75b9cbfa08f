class Com::Attr::LimitOption
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :schedule_option, Com::Attr::ScheduleOption.to_type
  attr_json :limit_count_option, Com::Attr::LimitCountOption.to_type

  def info(count: 0, time: nil)
    {
      limit_count_info: limit_count_option&.info(count),
      schedule_info: schedule_option&.info(time),
    }
  end
end
