# 行为
class Com::Attr::Action
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  CONDITION_CLASSES = [
    Com::Attr::Action::Condition::ConditionActiveRecordLifecycle,
    Com::Attr::Action::Condition::ConditionGroup,
    Com::Attr::Action::Condition::ConditionJsonata,
    # Com::Attr::Action::Condition::ConditionSourceMethod,
    Com::Attr::Action::Condition::ConditionOld,
    Com::Attr::Action::Condition::ConditionUser,
  ]

  CONDITION_CLASSES << Bpm::Attr::Action::Condition::ConditionBpmAction if defined?(Bpm)
  CONDITION_CLASSES << Bpm::Attr::Action::Condition::ConditionMultiBpmAction if defined?(Bpm)

  attr_json :name, :string # 行为的名称

  # 处理参数，转化为 hash 结果
  attr_json :pipeline, AttrJson::Type::PolymorphicModelWithDefault.new(
    Com::Attr::Action::Pipeline::PipelineEditor,
    Com::Attr::Action::Pipeline::PipelineJsonata,
    Com::Attr::Action::Pipeline::PipelineSourceMethod,
    Com::Attr::Action::Pipeline::PipelineFunction,
    Com::Attr::Action::Pipeline::PipelineDataForm,
    Com::Attr::Action::Pipeline::PipelineNone,
  )

  attr_json :condition, AttrJson::Type::PolymorphicModelWithDefault.new(
    *CONDITION_CLASSES
  )

  trigger_classes = [
    Com::Attr::Action::Trigger::TriggerActiveRecord,
    Com::Attr::Action::Trigger::TriggerFunction,
    Com::Attr::Action::Trigger::TriggerSourceMethod,
    Com::Attr::Action::Trigger::TriggerDataFormRecordMethod,
  ]

  trigger_classes << Bpm::Attr::Action::Trigger::TriggerCreateInstance if defined?(Bpm)
  trigger_classes << Bpm::Attr::Action::Trigger::TriggerCreateToken if defined?(Bpm)
  trigger_classes << Bpm::Attr::Action::Trigger::TriggerSmsNotify if defined?(Bpm)

  trigger_classes << Perf::Attr::Action::Trigger::TriggerCreateUnitByUnitDefine if defined?(Perf)

  attr_json :trigger, AttrJson::Type::PolymorphicModelWithDefault.new(
    *trigger_classes
  )

  def attrs(source:, payload: nil, **opts)
    pipline_result = get_pipeline_result(source: source, payload: payload, **opts)
    {
      source: source,
      payload: payload,
      **(pipline_result || {}),
      **opts
    }
  end

  def invoke(source:, payload: nil, **opts)
    return unless can_invoke?(source: source, payload: payload, **opts)
    trigger&.invoke(**attrs(source: source, payload: payload, **opts))
  end

  def can_invoke?(source:, payload: nil, **opts)
    condition ? condition.invoke(**attrs(source: source, payload: payload, **opts)) : false
  end

  def get_pipeline_result(**opts)
    pipeline ? pipeline.invoke(**opts) : opts
  end

  def self.send_by_permit_arguments target, method, *args, **opts
    defined_opts = []
    defined_all_opts = false
    target.method(method).parameters.each do |type, name|
      case type
      when :keyreq, :key
        defined_opts << name
      when :keyrest
        defined_all_opts = true
      end
    end

    if defined_all_opts
      opts = opts.symbolize_keys
    else
      opts = opts.symbolize_keys.slice(*defined_opts)
    end

    target.send(method, *args, **opts)
  end
end
