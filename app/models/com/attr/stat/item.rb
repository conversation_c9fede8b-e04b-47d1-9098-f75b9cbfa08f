# USAGE
#
# {"key"=>"group_item", "caculator"=>{"method"=>"group_sum", "group_attrs"=>["id"], "group_dates"=>[{"attr"=>"created_at", "by"=>"day", "options"=>{:format=>"%Y-%m-%d"}}], "type"=>"group_calc"}}
#
class Com::Attr::Stat::Item
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :_id, :string
  attr_json :key, :string
  attr_json :distinct, :boolean
  attr_json :filter, ActiveModel::Type::Value.new
  attr_json :joins, ActiveModel::Type::Value.new, array: true
  attr_json :left_joins, ActiveModel::Type::Value.new, array: true
  attr_json :scopes, ActiveModel::Type::Value.new
  attr_json :caculator, AttrJson::Type::PolymorphicModel.new(
    Com::Attr::Stat::GroupCalc,
    Com::Attr::Stat::Caculation,
    Com::Attr::Stat::Builder,
    # Com::Attr::Stat::MethodInvoke,
  )

  def calc(collection)
    association = collection.respond_to?(:limit) ?
      collection.unscope(:limit, :offset, :order) :
      collection

    if joins.present?
      joins_condition = joins.map { |join| join.is_a?(String) ? join.to_sym : join }
      association = association.joins(*joins_condition)
    end

    if left_joins.present?
      joins_condition = left_joins.map { |join| join.is_a?(String) ? join.to_sym : join }
      association = association.left_joins(*joins_condition)
    end

    # scopes 执行过滤
    association = Com::Invoker.invoke_chain(association, scopes) if scopes.present?

    # 获取过滤的数据
    association = association.ransack(filter).result.unscope(:order) if filter.present?
    association = association.distinct if distinct.present?
    stat_hash = caculator.calc(association)
    { key => stat_hash }
  end
end
