# USAGE:
#
# group_calc = Com::Attr::Stat::GroupCalc.new(
#   method: 'group_sum',
#   group_attrs: %w[id],
#   group_dates: [
#     { attr: 'created_at', by: 'day', options: { format: '%Y-%m-%d' } }
#   ],
# )
# User.ta_statistic(group_calc)

class Com::Attr::Stat::GroupCalc
  def self.name
    'group_calc'
  end

  class Date
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

    attr_json :_id, :string
    attr_json :name, :string # 重定义的名称，如果没有指定，则使用attr的名称
    attr_json :attr, :string
    attr_json :by, :string, default: 'day' # 如果是date，则代表使用groupdate的愈发
    # validates :by, inclusion_in: %w[
    #   day
    #   month
    #   week
    #   year
    #   quarter
    # ]
    attr_json :options, ActiveModel::Type::Value.new

    def key
      name || "#{attr}_by_#{by}"
    end
  end

  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :_id, :string
  attr_json :method, :string, default: 'group_sum'
  attr_json :group_attrs, :string, array: true
  attr_json :group_dates, Com::Attr::Stat::GroupCalc::Date.to_type, array: true
  attr_json :stat_attrs, :string, array: true
  attr_json :group_options, ActiveModel::Type::Value.new
  attr_json :stat_options, ActiveModel::Type::Value.new
  attr_json :order, :string
  attr_json :jsonata, :string

  # validates :method, inclusion_in: %w[
  #   group_sum
  #   group_count
  # ]

  def calc(association)
    group_association = association
    group_association = group_association.group(*group_attrs) if group_attrs.present?
    group_association = group_association.order(order) if order.present?
    if group_dates.present?
      group_dates.each do |group_date|
        group_association = group_association.send(
          "group_by_#{group_date.by}",
          group_date.attr,
          **(group_date.options || {}).symbolize_keys,
        )
      end
    end

    group_association_attrs =
      (group_attrs || []) +
      (group_dates&.map(&:key) || [])

    counter_storage = Com::CounterStorage.load(
      group_association_attrs,
      group_association.count,
      **(group_options || {}).symbolize_keys,
    )
    # 根据group的结果进行统计
    stat_hash = counter_storage.send(
      method,
      *(stat_attrs.present? ? stat_attrs : group_association_attrs),
      **(stat_options || {}).symbolize_keys,
    )
    # jsonata处理
    if jsonata.present?
      stat_hash = Jsonata.evaluate(
        jsonata,
        data: stat_hash,
      )
    end
    stat_hash
  end
end
