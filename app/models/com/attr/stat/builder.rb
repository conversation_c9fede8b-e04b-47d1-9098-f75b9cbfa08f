# USAGE:
#
# builder = Com::Attr::Stat::Builder.new(
#   units: [
#     {
#       name: '用户姓名',
#       chain: 'name',
#     },
#     {
#       name: '应用名称',
#       chain: 'app.name',
#     },
#   ],
# )
# User.ta_statistic(builder)

class Com::Attr::Stat::Builder
  def self.name
    'builder'
  end

  class Unit
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

    attr_json :_id, :string
    attr_json :name, :string # 重定义的名称，如果没有指定，则使用attr的名称
    attr_json :chain, ActiveModel::Type::Value.new
    # 可以循环嵌套
    attr_json :ref, Com::Attr::Stat::ReflectionItem.to_type

    def calc(record)
      if ref.present?
        { name => ref.calc(record) }
      else
        { name => Com::Invoker.invoke_chain(record, chain) }
      end
    end
  end

  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :_id, :string
  attr_json :filter, ActiveModel::Type::Value.new
  attr_json :order, :string
  attr_json :limit, :integer
  attr_json :distinct, :boolean, default: false
  attr_json :joins, ActiveModel::Type::Value.new, array: true
  attr_json :left_joins, ActiveModel::Type::Value.new, array: true
  attr_json :scopes, ActiveModel::Type::Value.new
  attr_json :units, Com::Attr::Stat::Builder::Unit.to_type, array: true

  def calc(association)
    if joins.present?
      joins_condition = joins.map { |join| join.is_a?(String) ? join.to_sym : join }
      association = association.joins(*joins_condition)
    end

    if left_joins.present?
      joins_condition = left_joins.map { |join| join.is_a?(String) ? join.to_sym : join }
      association = association.left_joins(*joins_condition)
    end

    # scopes 执行过滤
    association = Com::Invoker.invoke_chain(association, scopes) if scopes.present?
    association = association.ransack(filter).result if filter.present?
    association = association.distinct if distinct
    association = association.order(order) if order.present?
    association = association.limit(limit) if limit.present? && limit.positive?

    association.map do |record|
      units.each_with_object({}) do |unit, h|
        h.merge! unit.calc(record)
      rescue StandardError
        next
      end
    end
  end
end
