# h = {
#   refs: [
#     {
#       relations: ['execute_tour_task_actions'],
#       scopes: ['pending'],
#       item: {
#         key: 'task_stat',
#         caculator: {
#           type: 'caculation',
#           caculations: [
#             { name: '未巡', method: 'count' }
#           ],
#         },
#       },
#     },
#     {
#       relations: ['tasks'],
#       item: {
#         key: 'task_stat',
#         caculator: {
#           type: 'caculation',
#           caculations: [
#             { name: '正巡', method: 'count', filter: { stage_flag_in: %w[准备 了解] } },
#             { name: '已巡', method: 'count', filter: { stage_flag_not_in: %w[准备 了解] } }
#           ],
#         },
#       },
#     }
#
#   ],
# }
# stat_condition = Com::Attr::Stat::Resource.new h
# stat_result = resource.ta_statistic(stat_condition)

class Com::Attr::Stat::Resource
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :refs, Com::Attr::Stat::ReflectionItem.to_type, array: true

  def calc(resource)
    refs.each_with_object({}) do |ref, h|
      h.deep_merge!(ref.calc(resource))
    end
  end

  def present?
    super() && refs.present?
  end

  def blank?
    super() || refs.blank?
  end
end
