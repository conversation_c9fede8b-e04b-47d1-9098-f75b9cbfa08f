# USAGE:
#
# caculation = Com::Attr::Stat::Caculation.new(
#   caculations: [
#     {
#       name: '求和',
#       attr: 'id',
#       method: 'sum',
#     },
#     {
#       name: '最大值',
#       attr: 'id',
#       method: 'maximum',
#     }
#   ],
# )
# User.ta_statistic(caculation)

class Com::Attr::Stat::Caculation
  def self.name
    'caculation'
  end

  class Unit
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

    attr_json :_id, :string
    attr_json :name, :string # 重定义的名称，如果没有指定，则使用attr的名称
    attr_json :attr, :string
    attr_json :method, :string, default: 'sum'
    attr_json :filter, ActiveModel::Type::Value.new
    attr_json :group_attr, :string # 对于某些时候需要进行简单的分组计算
    attr_json :order, :string
    attr_json :limit, :integer
    attr_json :distinct, :boolean, default: false
    attr_json :use_map, :boolean, default: false
    attr_json :joins, ActiveModel::Type::Value.new, array: true
    attr_json :left_joins, ActiveModel::Type::Value.new, array: true
    attr_json :scopes, ActiveModel::Type::Value.new
    # validates :method, inclusion_in: %w[
    #   sum
    #   count
    #   average
    #   maximum
    #   minimum
    # ]
    def calc(association)
      if joins.present?
        joins_condition = joins.map { |join| join.is_a?(String) ? join.to_sym : join }
        association = association.joins(*joins_condition)
      end

      if left_joins.present?
        joins_condition = left_joins.map { |join| join.is_a?(String) ? join.to_sym : join }
        association = association.left_joins(*joins_condition)
      end

      # scopes 执行过滤
      association = Com::Invoker.invoke_chain(association, scopes) if scopes.present?
      association = association.ransack(filter).result if filter.present?
      association = association.distinct if distinct
      association = association.group(group_attr) if group_attr.present?
      association = association.order(order) if order.present?
      association = association.limit(limit) if limit.present? && limit > 0

      val =
        if attr.present?
          use_map ? association.map { |o| o.send(attr) }.send(method) : association.send(method, attr)
        else
          association.send(method)
        end
      name.present? ?
        { name => val } : { attr => val }
    end
  end

  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :_id, :string
  attr_json :caculations, Com::Attr::Stat::Caculation::Unit.to_type, array: true

  def calc(association)
    caculations.each_with_object({}) do |caculation, h|
      h.merge! caculation.calc(association)
    end
  end
end
