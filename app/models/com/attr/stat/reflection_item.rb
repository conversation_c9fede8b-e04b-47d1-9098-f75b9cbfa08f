class Com::Attr::Stat::ReflectionItem
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :relations, :string, array: true
  attr_json :scopes, ActiveModel::Type::Value.new
  attr_json :item, Com::Attr::Stat::Item.to_type

  def calc(resource)
    # 根据 relations 获取 collection
    collection = relations.reduce(resource) do |association, relation|
      # reflection_names = association._reflections.values.map { |r| r.name.to_s }
      # raise Error::TaStatParseError.new(message: "relation #{relation} not exist in collection") unless reflection_names.include?(relation)
      raise Error::TaStatParseError.new(message: "relation #{relation} not exist in collection") unless association.respond_to?(relation)

      association = association.send(relation)
      association
    end

    # scopes 执行过滤
    collection = Com::Invoker.invoke_chain(collection, scopes) if scopes.present?
    item.calc(collection)
  end
end
