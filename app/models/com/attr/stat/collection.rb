class Com::Attr::Stat::Collection
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :items, Com::Attr::Stat::Item.to_type, array: true

  def calc(collection)
    items.each_with_object({}) do |item, h|
      h.deep_merge!(item.calc(collection))
    end
  end

  def present?
    super() && items.present?
  end

  def blank?
    super() || items.blank?
  end
end
