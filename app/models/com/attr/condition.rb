class Com::Attr::Condition
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :name, :string # 条件的名称

  # groups 弃用
  attr_json :groups, Com::Attr::ConditionGroup.to_type, array: true

  def invoke source:, **opts
    return false unless action_condition.present?

    action_condition.invoke(source: source, **opts)
  end

  def valid? payload
    groups.any? { |group| group.valid?(payload) }
  end

  def self.rule_value payload, key
    payload.try(:condition_value, key) ||
      try_method(payload.try(:deep_stringify_keys) || payload, key) ||
      try_method(payload[:$root], key)
  end

  def self.try_chain record, arr
    arr.reduce(record) { |r, m| r.try(:[], m) || r.try(m) }
  end

  def self.try_method record, method
    arr = method.to_s.split(/\./)
    try_chain record, arr
  end
end
