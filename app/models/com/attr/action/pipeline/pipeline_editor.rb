class Com::Attr::Action::Pipeline::PipelineEditor
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :config, ActiveModel::Type::Value.new

  def self.name
    'editor'
  end

  def invoke(source:, payload: nil, **opts)
    # data_form = get_data_form(source: source)
    # {
    #   data_form: data_form,
    #   payload: data_form&.payload || payload || {},
    #   context: data_form&.record&.as_jbuilder_json || {}
    # }
  end
end
