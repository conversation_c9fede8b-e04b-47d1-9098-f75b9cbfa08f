class Com::Attr::Action::Pipeline::PipelineDataForm
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :form_conf_seq, :string # data_form seq

  def self.name
    'data_form'
  end

  def get_data_form(source:, **_opts)
    # @data_form ||= form_conf_seq && source.find_data_form_by_seq(form_conf_seq)
    # @data_form
    form_conf_seq && source.find_data_form_by_seq(form_conf_seq)
  end

  def invoke(source:, payload: nil, **opts)
    data_form = get_data_form(source: source)
    {
      data_form: data_form,
      payload: data_form&.payload || payload || {},
      context_proc: proc { data_form&.record&.as_jbuilder_json || {} }
    }
  end
end
