class Com::Attr::Action::Pipeline::PipelineSourceMethod
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :name, :string
  attr_json :method, :string, default: 'itself'
  attr_json :callback_params, ActiveModel::Type::Value.new

  def self.name
    'source_method'
  end

  def invoke(source:, payload:, **opts)
    result = source.try_method(
      method,
      **opts,
      **(callback_params || {}).symbolize_keys
    )
    { payload: result.is_a?(Hash) ? result : { :$root => result } }
  end
end
