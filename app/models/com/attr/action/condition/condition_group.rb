class Com::Attr::Action::Condition::ConditionGroup
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  # attr_json :form_conf_seq, :string

  # attr_json :children_relation_type, :string, default: 'and' # and or
  # attr_json :children, AttrJson::Type::PolymorphicModel.new(
  #   *Com::Attr::Action::CONDITION_CLASSES
  # ), array: true, default: []
  attr_json :name, :string
  attr_json :actions_relation_type, :string, default: 'and' # and or
  attr_json :actions, Com::Attr::Action.to_type, array: true

  def self.name
    'group'
  end

  def invoke(source:, payload: nil, context: {}, context_proc: proc { {} }, **opts)
    arguments = {
      source: source, payload: context.dup.merge(context_proc.call || {}).merge(payload || {}), **opts
    }
    case actions_relation_type
    when 'or'
      actions.any? do |action|
        action.can_invoke?(**arguments)
      end
    else
      actions.all? do |action|
        action.can_invoke?(**arguments)
      end
    end
  end
end
