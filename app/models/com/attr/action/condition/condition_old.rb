# 老版本的 condition
class Com::Attr::Action::Condition::ConditionOld
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :name, :string
  attr_json :rule, AttrJson::Type::PolymorphicModel.new(
    Com::Attr::ConditionRules::Number,
    Com::Attr::ConditionRules::<PERSON>ole<PERSON>,
    Com::Attr::ConditionRules::String,
    Com::Attr::ConditionRules::SingleChoice,
    Com::Attr::ConditionRules::MultiChoice,
  )

  attr_json :desc, ActiveModel::Type::Value.new

  def self.name
    'old'
  end

  def invoke(payload:, context: {}, context_proc: proc {{}}, **opts)
    rule.valid?(
      begin
        context.dup.merge(context_proc.call || {}).merge(payload)
      # 有时 payload 是对象
      rescue TypeError
        payload
      end
    )
  end
end
