class Com::Attr::Action::Condition::ConditionUser
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  class DepartmentIn
    include AttrJson::Model

    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_ids, :integer, array: true

    def self.name
      'department_in'
    end

    def valid?(user)
      (user.department_ids & (source_ids || [])).present?
    end
  end

  class OrgIn
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_ids, :integer, array: true

    def self.name
      'org_in'
    end

    def valid?(user)
      (user.org_ids & (source_ids || [])).present?
    end
  end

  class DutyIn
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_ids, :integer, array: true

    def self.name
      'duty_in'
    end

    def valid?(user)
      (user.duty_ids & (source_ids || [])).present?
    end
  end

  class RoleIn
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_ids, :integer, array: true

    def self.name
      'role_in'
    end

    def valid?(user)
      (user.role_ids & (source_ids || [])).present?
    end
  end

  class DepartmentCont
    include AttrJson::Model

    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_name, :string

    def self.name
      'department_cont'
    end

    def valid?(user)
      user.departments.map(&:name).any? { |name| name.to_s.include?(source_name) }
    end
  end

  class OrgCont
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_name, :string

    def self.name
      'org_cont'
    end

    def valid?(user)
      user.orgs.map(&:name).any? { |name| name.to_s.include?(source_name) }
    end
  end

  class DutyCont
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_name, :string

    def self.name
      'duty_cont'
    end

    def valid?(user)
      user.duties.map(&:name).any? { |name| name.to_s.include?(source_name) }
    end
  end

  class RoleCont
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :source_name, :string

    def self.name
      'role_cont'
    end

    def valid?(user)
      user.roles.map(&:name).any? { |name| name.to_s.include?(source_name) }
    end
  end

  class DutyRank
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
    attr_json :ranks, :string, array: true

    def self.name
      'duty_rank'
    end

    def valid?(user)
      (user.duties.map(&:rank) & (ranks || [])).present?
    end
  end

  attr_json :user_rule, AttrJson::Type::PolymorphicModel.new(
    DepartmentIn, # 部门
    OrgIn, # 组织
    DutyIn, # 岗位
    RoleIn, # 权限
    DepartmentCont, # 部门名包含
    OrgCont, # 组织名包含
    DutyCont, # 岗位名包含
    RoleCont, # 权限名包含
    DutyRank, # 岗位等级
  )
  attr_json :user_method, :string, default: 'user'

  def self.name
    'user'
  end

  def invoke(source: , **opts)
    user = source.try_method(user_method)
    user.blank? ? false : user_rule.valid?(user)
  end
end
