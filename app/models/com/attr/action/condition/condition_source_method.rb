# 预置 condition
# 应该通过 pipline source method 来实现
class Com::Attr::Action::Condition::ConditionSourceMethod
  # include AttrJson::Model
  # attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  # attr_json :method, :string, default: 'state'
  # attr_json :target_value, ActiveModel::Type::Value.new # 使用 json editor

  # def self.name
  #   'source_method'
  # end

  # def invoke(source:, **opts)
  #   source.try_method(method) == target_value
  # end
end
