class Com::Attr::Action::Trigger::TriggerSourceMethod
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  attr_json :name, :string
  attr_json :callback_method, :string
  attr_json :callback_params, ActiveModel::Type::Value.new

  def self.name
    'source_method'
  end

  def invoke(source:, **opts)
    Com::Attr::Action.send_by_permit_arguments(
      source,
      callback_method.to_s.strip,
      _payload: opts[:payload],
      **opts.except(:payload),
      **(callback_params || {}).symbolize_keys,
    )
  end
end
