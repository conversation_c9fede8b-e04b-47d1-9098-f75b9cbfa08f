class Com::Attr::Action::Trigger::TriggerActiveRecord
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  attr_reader :klass_singular

  attr_json :name, :string
  attr_json :parent_klass, :string
  attr_json :parent_id, :string
  attr_json :parent_form_conf_seq, :string
  attr_json :association, :string
  attr_json :klass, :string
  attr_json :default_values, ActiveModel::Type::Value.new

  def self.name
    'active_record'
  end

  def serializable_hash(*args)
    super.merge(
      klass_singular: @klass_singular ||= klass&.constantize&.model_name&.singular,
    )
  end

  def invoke(source:, data_form:, **opts)
    data_form.transaction do
      if parent_form_conf_seq.present?
        parent_resource = source.find_data_form_by_seq(parent_form_conf_seq).record
        begin_of_record =
          if parent_resource.present? && parent_resource._reflections.values.map(&:name).map(&:to_s).include?(association)
            parent_resource.send(association)
          else
            begin
              klass.constantize
            rescue NoMethodError
              raise Error::BaseError.new(message: 'parent_form_conf_seq 对应的 record 为空')
            end
          end
      elsif parent_klass.present? && parent_id.present? && association.present?
        parent_resource = parent_klass.constantize.find_by(id: parent_id)
        begin_of_record =
          if parent_resource.present? && parent_resource._reflections.values.map(&:name).map(&:to_s).include?(association)
            parent_resource.send(association)
          else
            klass.constantize
          end
      elsif klass.present?
        begin_of_record = klass.constantize
      else
        begin_of_record = nil
      end

      return if begin_of_record.nil?

      new_record = begin_of_record.ta_create(
        attributes: (default_values&.to_h || {}).merge(data_form.payload || {}).symbolize_keys.except(:app, :user, :creator),
        extra: {
          app_id: data_form.source.app_id,
          user: data_form.source.try(:user) || data_form.source.try(:creator),
          creator: data_form.source.try(:user) || data_form.source.try(:creator),
        },
      )

      data_form.update!(
        record: new_record,
        payload: new_record.as_jbuilder_json
      )
      new_record
    end
  end
end
