class Com::Attr::Action::Trigger::TriggerFunction
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :name, :string
  attr_json :callback_class_name, :string # 回调的类名
  attr_json :callback_method, :string
  attr_json :callback_params, ActiveModel::Type::Value.new

  def self.name
    'function'
  end

  def invoke(source:, payload:, **opts)
    Com::Attr::Action.send_by_permit_arguments(
      callback_class_name.to_s.strip.constantize,
      callback_method.to_s.strip,
      _payload: payload,
      _source: source,
      token: source,
      **opts,
      **(callback_params || {}).symbolize_keys
    )
  end
end
