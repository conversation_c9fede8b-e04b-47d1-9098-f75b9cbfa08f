# 预置的 trigger
# data_form 关联对象的 方法执行
class Com::Attr::Action::Trigger::TriggerDataFormRecordMethod
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  attr_json :name, :string
  attr_json :callback_method, :string
  attr_json :callback_params, ActiveModel::Type::Value.new

  def self.name
    'data_form_record_method'
  end

  def invoke(source:, data_form: nil, **opts)
    # 这里存在 flowable 直接复写 ref_model_setting_form_setting 的情况，导致 workflow 无法拿到 flowable form_setting，无法关联
    # 暂时未找到好方法
    record = data_form&.record ? data_form.record : (source.try(:instance).try(:flowable) || source.try(:flowable))

    Com::Attr::Action.send_by_permit_arguments(
      record,
      callback_method.to_s.strip,
      data_form_source: source,
      token: source, # 兼容老方法
      _payload: opts.symbolize_keys[:payload],
      **opts.symbolize_keys.except(:payload),
      **(callback_params || {}).symbolize_keys,
    )
  end
end
