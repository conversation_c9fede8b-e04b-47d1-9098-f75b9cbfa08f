module Com::Attr::Action::Concern::ActsAsActionable
  extend ActiveSupport::Concern

  included do
    include Com::Ext::DataFormSource

    def find_data_form_by_seq seq
      data_form = nil
      data_form_association_names.each do |data_form_association_name|
        next if data_form
        # nested attributes 导致这里会返回一些 id nil 的 结果
        data_form = send(data_form_association_name).select { |obj| obj.form_conf_seq == seq }.first
        # data_form = send(data_form_association_name).where(form_conf_seq: seq).first
      end
      data_form
    end

    def invoke_actions(source: self, **opts)
      form_settings.invoke_each_action(source: source, **opts)
    end

    def any_invoke_actions?(source: self, **opts)
      form_settings.any_can_invoke?(source: source, **opts)
    end

    # before_create :invoke_before_create

    # def invoke_before_create
    #   invoke_actions(lifecycle: :before_create, source: self)
    # end

    # before_save :invoke_before_save

    # def invoke_before_save
    #   invoke_actions(lifecycle: :before_save, source: self)
    # end

    # NOTE: 为了保证执行顺序在 nested_attributes data_form 之后
    # 这里回调需要自己注册，需要在 form_setting_with 之后。

    after_create :invoke_after_create

    def invoke_after_create
      invoke_actions(lifecycle: :after_create, source: self)
    end

    after_save :invoke_after_save

    def invoke_after_save
      invoke_actions(lifecycle: :after_save, source: self)
    end
  end
end
