class Com::Attr::ConditionItem
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :_id, :string
  attr_json :name, :string
  attr_json :rule, AttrJson::Type::PolymorphicModel.new(
    Com::Attr::ConditionRules::Number,
    Com::Attr::ConditionRules::<PERSON><PERSON><PERSON>,
    Com::Attr::ConditionRules::String,
    Com::Attr::ConditionRules::SingleChoice,
    Com::Attr::ConditionRules::MultiChoice,
  )

  attr_json :desc, ActiveModel::Type::Value.new

  def valid? payload
    rule.valid? payload
  end
end
