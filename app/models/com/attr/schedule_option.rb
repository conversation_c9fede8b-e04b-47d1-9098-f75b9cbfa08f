class Com::Attr::ScheduleOption
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :opt, :string, default: 'none'
  # validates :opt, inclusion_in: ['none', 'schedule', 'once']
  attr_json :schedule_type, :string
  # minute, hour, day, month, year
  attr_json :schedule_val, :float
  attr_json :time_val, :datetime

  def info(time = nil)
    case opt
    when 'none'
      { time: time, next_time: nil }
    when 'schedule'
      { time: time, next_time: (time || Time.zone.now) + schedule_val.send(schedule_type) }
    when 'once'
      { time: time, next_time: time_val }
    end
  end
end
