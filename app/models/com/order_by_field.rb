module Com::OrderByField
  # Usage
  # Tech::Record.where(id: ids).extend(Com::OrderByField).order_by_field(:id, ids)
  def order_by_field(column, custom_list, sort_order = :asc)
    return self if custom_list.blank?

    model = self.class.to_s.deconstantize.constantize
    num = custom_list.length
    dml = <<~DML.squish
      CASE #{column}
        #{(0...num).map { |i| "WHEN ? THEN #{i}" }.join("\n")}
        ELSE #{num}
      END
      #{sort_order.upcase}
    DML

    order(
      Arel.sql(
        model.send(:sanitize_sql_array, [dml, *custom_list]),
      ),
    )
  end
end
