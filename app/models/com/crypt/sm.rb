module Com::Crypt::Sm
  class Sm4
    class << self
      SM4_KEY = ENV.fetch('SM4_KEY', 'tallty.com123456')

      # sm4 ecb以及cbc简单模式加密/解密
      def encrypt(data, key: nil, mode: 'ecb', iv: nil)
        data = data.is_a?(Hash) ? data.to_json : data.to_s
        cipher = OpenSSL::Cipher.new("sm4-#{mode}")
        cipher.encrypt
        cipher.key = key || SM4_KEY
        # cbc模式
        if iv
          iv = cipher.random_iv
          cipher.iv = iv
        end
        encrypted_data = cipher.update(data) + cipher.final
        data = encrypted_data.unpack1('H*')
        iv ? { iv: iv.unpack1('H*'), data: data } : data
      end

      def decrypt(data, key: nil, mode: 'ecb', iv: nil)
        data = [data].pack('H*')
        cipher = OpenSSL::Cipher.new("sm4-#{mode}")
        cipher.decrypt
        cipher.key = key || SM4_KEY
        cipher.iv = [iv].pack('H*') if iv
        (cipher.update(data) + cipher.final).force_encoding('utf-8')
      end

      ### cbc模式加密
      def iv_encrypt(data, key: nil)
        encrypt(data, key: key, mode: 'cbc', iv: true)
      end

      def iv_decrypt(data, iv, key: nil)
        decrypt(data, key: key, mode: 'cbc', iv: iv)
      end

      alias_method :easy_decrypt, :decrypt
      alias_method :easy_encrypt, :encrypt
    end
  end

  class Sm3
    class << self
      def encrypt(data)
        OpenSSL::Digest.new("sm3", data).to_s
      end

      alias_method :easy_encrypt, :encrypt
    end
  end
end