class Com::Crypt::Aes
  class << self
    def easy_encrypt(raw_data, iv: nil)
      raw_data = raw_data.is_a?(String) ? raw_data : raw_data.to_json
      password = Digest::SHA256.digest(ENV.fetch('IV_KEY', 'tallty.com'))

      iv = iv.present? ? Digest::MD5.digest(iv) : iv
      encrypt(password, iv, raw_data)
    end

    def easy_decrypt(secret_data, iv: nil)
      return secret_data if secret_data.blank?

      iv = iv.present? ? Digest::MD5.digest(iv) : iv
      password = Digest::SHA256.digest(ENV.fetch('IV_KEY', 'tallty.com'))
      begin
        raw_data = decrypt(password, iv, secret_data)
      rescue OpenSSL::Cipher::CipherError
        return nil
      end

      begin
        JSON.parse(raw_data)
      rescue StandardError
        raw_data
      end
    end

    # password，Digest::SHA256.digest
    # cleardata is to_json string
    def encrypt(password, iv, cleardata)
      cipher = OpenSSL::Cipher.new('AES-256-CBC')
      cipher.encrypt  # set cipher to be encryption mode
      cipher.key = password
      cipher.iv  = iv unless iv.nil?

      encrypted = ''
      encrypted << cipher.update(cleardata)
      encrypted << cipher.final
      b64enc(encrypted)
    end

    def decrypt(password, iv, secretdata)
      secretdata = Base64.decode64(secretdata)
      decipher = OpenSSL::Cipher.new('AES-256-CBC')
      decipher.decrypt
      decipher.key = password
      decipher.iv = iv unless iv.nil?
      decipher.update(secretdata) + decipher.final
    end

    def b64enc(data)
      Base64.encode64(data).gsub(/\n/, '')
    end

    def random_iv
      cipher = OpenSSL::Cipher.new('AES-256-CBC')
      cipher.random_iv
    end
  end
end
