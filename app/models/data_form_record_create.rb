# 1. 创建对象触发，data_form状态变更同步更新对象的状态，另外在进行中的时候，可以通过data_form去修改关联对象

# 跟随表单更新对象
# DataFormSyncRecord
class DataFormRecordCreate < DataForm
  after_save :update_record, if: :payload_cache
  after_save :complete_record, if: -> {
    saved_change_to_state? && state == 'completed' && record.respond_to?(:auto_complete_from_data_form!)
  }

  def update_record
    return unless state == 'processing' || state == 'created' || state == nil

    record&.ta_update(
      attributes: payload_cache,
    )
  end

  def async_source
    update_columns(payload: record.as_jbuilder_json)
  end

  def complete_record
    record.auto_complete_from_data_form!
  end
end
