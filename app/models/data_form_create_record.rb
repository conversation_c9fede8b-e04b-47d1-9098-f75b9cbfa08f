# # 4. 流程通过后，根据data_form的设置，创建对应的record对象，这里需要注意有可能有association的情况需要考虑

class DataFormCreateRecord < DataForm
#   serialize :options, Com::Attr::Action::TriggerActiveRecord.to_serialization_coder

#   after_save :create_record, if: :saved_change_to_state?

#   def create_record
#     return unless state == 'completed'
#     return unless options.present?

#     options.invoke(source: self)
#   end
end
