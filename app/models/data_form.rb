class DataForm < ApplicationRecord
  include Com::Model::DataForm

  # STI支持以下功能扩展：
  # 1. 创建对象触发，data_form状态变更同步更新对象的状态，另外在进行中的时候，可以通过data_form去修改关联对象
  # 2. 更新对象触发，data_form状态通过后，触发修改对象的属性
  # 3. 删除对象触发，data_form状态通过后，触发删除对象的操作
  # 4. 流程通过后，根据data_form的设置，创建对应的record对象，这里需要注意有可能有association的情况需要考虑

  after_save :invoke_actions, if: :saved_change_to_state?

  def actions
    # logger.error 'data_form actions==================================='
    source.form_settings.find_actions_by_seq(form_conf_seq)
  end

  def invoke_actions
    # return unless state == 'completed'

    actions.each do |action|
      action.invoke(source: source, payload: payload, state: state)
    end
  end
end
