class Transfer::Abstract
  attr_reader :extract_conf, :data, :options, :mode, :wrap_key

  def initialize(extract_conf, data, wrap_key: nil, mode: 'single', **options)
    @extract_conf = extract_conf
    @data = data
    @wrap_key = wrap_key
    @mode = mode
    @options = options
  end

  ### 测试extract_conf
  # conf = {
  #   reflections: [
  #     { key: 'users', name: 'users', klass: 'user' },
  #   ],
  #   attrs: [
  #     { key: 'id', name: 'id', cast_type: 'integer' },
  #     { key: 'name', name: 'name' },
  #   ]
  # }
  # ModelConf.create(name: 'app', klass: 'app', conf: conf)

  # conf = {
  #   reflections: [
  #     { key: 'user', name: 'user', klass: 'user' },
  #   ],
  #   attrs: [
  #     { key: 'id', name: 'id', cast_type: 'integer' },
  #     { key: 'user_id', name: 'user_id' },
  #   ]
  # }
  # ModelConf.create(name: 'member', klass: 'member', conf: conf)

  # conf = {
  #   reflections: [
  #     { key: 'app', name: 'app', klass: 'app' },
  #     { mode: 'many', key: 'members', name: 'members', klass: 'member'}
  #   ],
  #   attrs: [
  #     { key: 'id', name: 'id', cast_type: 'integer' },
  #     { key: 'name', name: 'name' },
  #   ]
  # }
  # ModelConf.create(name: 'user', klass: 'user', conf: conf)

  # member_units = [
  #   { name: 'id', key: 'id', model_key: 'id', type: 'attribute' },
  #   { name: 'user_id', key: 'user_id', model_key: 'user_id', type: 'attribute' },
  #   { name: 'user_name', key: 'user_name', chain: 'user.name', type: 'custom_attribute' }
  # ]
  # member_conf = Forms::Attr::ExtractConf.new(klass: 'member', units: member_units)

  # app_units = [
  #   { name: 'id', key: 'id', model_key: 'id', type: 'attribute' },
  #   { name: 'name', key: 'name', model_key: 'name', type: 'attribute' },
  # ]
  # app_conf = Forms::Attr::ExtractConf.new(klass: 'app', units: app_units)

  # units = [
  #   { name: 'id', key: 'id', model_key: 'id', type: 'attribute' },
  #   { name: 'name', key: 'name', model_key: 'name', chain: 'name', type: 'attribute' },
  #   { name: 'app', key: 'app', model_key: 'app', conf: app_conf, type: 'conf' },
  #   { name: 'members', key: 'members', model_key: 'members', conf: member_conf, type: 'conf_array' }
  # ]
  # extract_conf = Forms::Attr::ExtractConf.new(klass: 'user', units: units)

  ### single 单表模式
  # data = User.first
  # result = Transfer::Abstract.new(extract_conf, data).transfer
  ### collection 集合
  # data = User.all
  # result = Transfer::Abstract.new(extract_conf, data, mode: 'collection').transfer

  def transfer
    result = case mode
               when 'collection'
                 extract_conf.extract_collection(data, **options)
               when 'single'
                 extract_conf.extract(data, **options)
               else
                 raise 'please check args'
             end
    wrap_key ? { "#{wrap_key}": result } : result
  end
end