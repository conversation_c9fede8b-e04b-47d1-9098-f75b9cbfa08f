module Auth::Authable
  extend ActiveSupport::Concern

  included do
    include Auth::CallSoaAuth
    attr_accessor :auth_id
  end

  # 增加第三方平台，通过账号换token的需求
  def login!
    account_info = {
      app_id: app.code,
      account: account,
      account_type: self.class.name,
      user_id: id,
    }
    Auth::AuthToken.create(
      account_info: account_info,
    )
  end

  module ClassMethods
    def auth!(token)
      user_info = Auth::AuthToken.auth! token
      user_info['account_type'] ||= user_info['type']
      user_info['account'] ||= user_info['code']
      type = user_info['account_type']

      raise Error::TokenError unless type.present?

      user_info
    end

    def idcode(idcode)
      Auth::AuthToken.auth! idcode
    end
  end
end
