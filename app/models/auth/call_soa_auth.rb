module Auth::CallSoaAuth
  extend ActiveSupport::Concern

  included do
    class_attribute :skip_callback

    # code
    # password
    # both
    # none
    # nil
    attr_accessor :account_mode

    after_commit :call_create_soa_auth!,  on: :create
    after_commit :call_destroy_soa_auth!, on: :destroy
    after_commit :call_update_soa_auth!, on: :update, if: :can_call_update_soa_auth?
  end

  def call_create_soa_auth!
    if defined? SoaAuth::AuthAccount
      case account_mode.to_s
      when 'none'
        return
      else
        auth_account = auth_accounts.find_or_initialize_by(
          app_id: app_code,
          account: account
        )
        auth_account.assign_attributes(
          password_raw: set_default_password_raw,
          account_type: self.class.to_s,
          account_mode: account_mode
        )
        auth_account.save!
      end
    elsif env_production?
      body = {
        app_id: app_code,
        password_raw: set_default_password_raw,
        account: account,
        account_type: self.class.to_s,
      }
      call_soa_auth!(body: body, url: 'api/auth_accounts')
    end
  end

  def call_destroy_soa_auth!
    if defined? SoaAuth::AuthAccount
      auth_accounts&.destroy_all

      SoaAuth::AuthAccount.where(
        app_id: app_code,
        account: account,
      ).destroy_all
    elsif env_production?
      body = {
        app_id: app_code,
        account: account,
      }
      call_soa_auth!(body: body, url: 'api/auth_accounts/cancel')
    end
  end

  def call_update_soa_auth!
    if defined? SoaAuth::AuthAccount
      h = {}
      h[:password_raw] = password_raw if password_raw.present?
      h[:account] = account if saved_change_to_account?

      auth_accounts.each { |auth_account| auth_account.update(h) } if h.present?
      auth_account&.update(h) if h.present? && auth_accounts.blank?
    elsif account_exists? && env_production?
      body = {
        app_id: app_code,
        password_raw: password_raw,
        account: account,
        account_type: self.class.to_s,
      }
      call_soa_auth!(body: body, url: 'api/auth_accounts/1', method: :path)
      self.password_raw = nil
    else
      call_create_soa_auth!
    end
  end

  def call_soa_auth!(url:, body: {}, method: :post)
    headers = { Accept: 'application/json' }
    body = body.merge(auth_account: body)
    url = File.join(ENV['SOA_AUTH_URL'], url)
    begin
      if method.to_s == 'post'
        Typhoeus.post(url, headers: headers, body: body)
      elsif method.to_s == 'get'
        Typhoeus.get(url, headers: headers, body: body)
      else
        Typhoeus.patch(url, headers: headers, body: body)
      end
    rescue StandardError
      # 加入重试机制
      # retry
    end
  end

  def account_exists?
    body = {
      app_id: app_code,
      account: account,
    }
    res = call_soa_auth!(body: body, url: 'api/auth_accounts/1', method: :get)
    res.success?
  end

  private

  def env_production?
    Rails.env.production? && skip_callback.blank?
  end

  def can_call_update_soa_auth?
    # 原有逻辑：密码变更时触发
    # 新增逻辑：account变更时也触发（向后兼容）
    (env_production? && password_raw.present?) || saved_change_to_account?
  end

  def set_default_password_raw
    password_raw || SecureRandom.hex(16)#'abcd1234'
  end
end
