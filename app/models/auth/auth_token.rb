module Auth
  class AuthToken
    EXPIRE = ENV['REIDS_EXPIRE']&.to_i || 60 * 60 * 24 * 7 * 365

    def self.create(account_info:, token: nil, expire: EXPIRE)
      token ||= SecureRandom.uuid
      $token_redis.setex token, expire, account_info.merge(
        token: token,
      ).to_json
      auth! token
    end

    def self.destroy(token)
      $token_redis.del token
    end

    def self.auth!(token)
      auth_info = JSON.parse $token_redis.get(token)

      # 重新刷新过期时间
      ttl = $token_redis.ttl(token)
      $token_redis.expire token, EXPIRE if ttl > 0 && ttl < EXPIRE

      auth_info
    rescue StandardError
      raise Error::TokenError
    end
  end
end
