class Forms::Attr::Action
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :_id, :string
  attr_json :key, :string
  attr_json :label, :string
  attr_json :enabled, :boolean
  attr_json :icon, :string
  attr_json :confirm, :boolean
  attr_json :action_type, :string
  attr_json :callback, ActiveModel::Type::Value.new
  attr_json :options, ActiveModel::Type::Value.new
  attr_json :collapsed, :boolean
end
