class Forms::Attr::FormConf
  # 表单信息：可以包含引用表单、复制表单两种方式，其中引用表单，使用的还是model_setting的方式
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  attr_json :seq, :string, default: -> { SecureRandom.hex(10) }
  attr_json :name, :string

  conf_classes = [
    Forms::Attr::Conf::Copy,
    Forms::Attr::Conf::ModelSettingRef,
  ]

  conf_classes << Perf::Forms::Attr::Conf::UnitDefineConf if defined?(Perf)

  attr_json :conf, AttrJson::Type::PolymorphicModelWithDefault.new(
    *conf_classes
  )

  attr_json :data_form_type, :string
  # options 提供给类似 token_source_options 使用
  attr_json :options, ActiveModel::Type::Value.new

  attr_accessor :form_setting

  # def data_form_klass
  #   self.form_setting&.seq_2_data_form_type&.[](seq)
  # end

  def form
    conf&.form
  end

  def form_empty?
    form&.form_empty?
  end

  def create_or_update_data_form(
    find_attributes: {},
    extra_attributes: {},
    source: nil,
    data_form_association_name: :data_forms
  )
    attributes = extra_attributes.symbolize_keys
    # .merge(
    #   data_form_klass ? { type: data_form_klass } : {},
    # )

    if block_given?
      yield attributes
    elsif source.respond_to?("update_#{data_form_association_name}_by_form_conf")
      source.send("update_#{data_form_association_name}_by_form_conf", self, find_attributes, attributes)
    else
      raise Error::BaseError.new(message: '错误的调用方式，请检查')
    end
  end
end
