class Forms::Attr::ExtractConf
  # 可以配置从record中抽取相关的内容
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  class Attribute
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    # 对应model attribute的key
    attr_json :model_key, :string
    # 自身需要转成的key
    attr_json :key, :string

    def self.name
      'attribute'
    end

    def extract record, extract_conf, *options
      value_key = key.presence || model_key
      { value_key => extract_conf.model_conf&.find_attribute(model_key)&.extract_value(record, *options) }
    end
  end

  class CustomAttribute
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    # 属性名称
    attr_json :name, :string
    # 对象的key
    attr_json :key, :string
    # 抽取方法
    attr_json :chain, ActiveModel::Type::Value.new

    def self.name
      'custom_attribute'
    end

    def extract record, extract_conf, *options
      value_chain = chain.presence || key
      { key => Com::Invoker.invoke_chain(record, value_chain) }
    end
  end

  class Conf
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    # 对象名称
    attr_json :name, :string
    # 对象的key
    attr_json :key, :string
    # 模型对象的key
    attr_json :model_key, :string
    # 抽取方法
    attr_json :chain, ActiveModel::Type::Value.new
    # 具体关联的配置
    attr_json :conf, Forms::Attr::ExtractConf.to_type

    def self.name
      'conf'
    end

    def extract record, extract_conf, *options
      value_key = key.presence || model_key
      value_chain = chain.presence || model_key
      value_record = Com::Invoker.invoke_chain(record, value_chain)
      { value_key => conf.extract(value_record, *options) }
    end
  end

  class ConfArray
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    # 对象名称
    attr_json :name, :string
    # 对象的key
    attr_json :key, :string
    # 模型对象的key
    attr_json :model_key, :string
    # 抽取方法
    attr_json :chain, ActiveModel::Type::Value.new
    # 具体关联的对象列表
    attr_json :conf, Forms::Attr::ExtractConf.to_type

    def self.name
      'conf_array'
    end

    def extract record, extract_conf, *options
      value_key = key.presence || model_key
      value_chain = chain.presence || model_key
      value_records = Com::Invoker.invoke_chain(record, value_chain)
      { value_key => value_records.map { |value_record| conf.extract(value_record, *options) } }
    end
  end

  # attr_json :uid, :string, default: -> { SecureRandom.hex(10) }
  attr_json :klass, :string
  attr_json :units, AttrJson::Type::PolymorphicModelWithDefault.new(
    Conf,
    ConfArray,
    Attribute,
    CustomAttribute,
  ), array: true

  # 根据klass找到model_conf
  def model_conf(*args)
    @model_conf ||= ModelConf.load_with_klass(klass)
  end

  def extract record, *options
    units.each_with_object({}) do |unit, h|
      h.merge! unit.extract(record, self, *options)
    end
  end

  def extract_collection(records, *options)
    records.map do |record|
      extract(record, *options)
    end
  end
end
