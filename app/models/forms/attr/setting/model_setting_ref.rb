class Forms::Attr::Setting::ModelSettingRef
  include AttrJson::Model

  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
  # klass_singular#model_setting
  attr_json :model_setting_identity, :string
  attr_json :setable_type, :string
  attr_json :setable_id, :string

  def extra_options
    model_setting&.form_setting&.setting&.extra_options
  end

  def self.name
    'model_setting'
  end

  def model_setting
    klass_singular, flag = model_setting_identity.to_s.split('#')
    ModelDefine.model_setting_by_singular_name(
      klass_singular,
      flag: flag,
      setable_type: setable_type,
      setable_id: setable_id,
    )
  end

  def confs
    model_setting&.form_setting&.setting&.confs
  end

  def serializable_hash(*args)
    super.merge(
      confs: confs,
      extra_options: extra_options,
    )
  end

  def form_empty?
    confs ? confs.all?(&:form_empty?) : true
  end
end
