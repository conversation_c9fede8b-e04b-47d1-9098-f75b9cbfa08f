class Forms::Attr::Setting::TemplateRef
  include AttrJson::Model

  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)
  attr_json :form_uuid, :string

  def extra_options
    template&.form_setting&.setting&.extra_options
  end

  def self.name
    'template'
  end

  def form_empty?
    confs ? confs.all?(&:form_empty?) : true
  end

  def template
    Forms::Template.find_by(uuid: form_uuid)
  end

  def confs
    template&.form_setting&.setting&.confs
  end

  def serializable_hash(*args)
    super.merge(
      confs: confs,
      extra_options: extra_options,
    )
  end
end
