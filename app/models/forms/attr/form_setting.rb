class Forms::Attr::FormSetting
  include AttrJson::Model

  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  attr_json :seq, :string, default: -> { SecureRandom.hex(10) }
  attr_json :name, :string
  attr_json :layout, :string
  attr_json :setting, AttrJson::Type::PolymorphicModelWithDefault.new(
    Forms::Attr::Setting::Copy,
    Forms::Attr::Setting::ModelSettingRef,
    Forms::Attr::Setting::TemplateRef,
  )
  attr_json :options, ActiveModel::Type::Value.new # 储存前端配置，例如 编辑组件、预览组件等

  attr_json :actions, Com::Attr::Action.to_type, array: true, default: []

  def serializable_hash(*args)
    super.merge(
      form_empty: form_empty?
    )
  end

  def form_empty?
    setting&.form_empty?
  end

  def confs
    result = setting&.confs || []
    result.each do |conf|
      conf.form_setting = self
    end
    result
  end

  def invoke_each_action source:, **opts
    actions.map do |action|
      action.invoke(source: source, **opts)
    end
  end

  def any_can_invoke? source:, **opts
    actions.any? do |action|
      action.can_invoke?(source: source, **opts)
    end
  end

  def merge_form_confs form_confs, actions: []
    Forms::Attr::FormSetting.new({
      **as_json,
      actions: [*(actions || []), *(self.actions || [])],
      setting: {
        type: 'copy',
        **(setting.as_json || {}),
        confs: [
          *(setting&.confs || []),
          *form_confs
        ].reverse.uniq(&:seq).reverse
      }
    })
  end

  def find_actions_by_seq(seq)
    actions.select { |action| action.pipeline.try(:form_conf_seq) == seq }
  end

  def create_or_update_data_form_by_seq(
    seq:,
    find_attributes: {},
    extra_attributes: {},
    source: nil,
    data_form_association_name: :data_forms,
    &save_block
  )
    current_conf = confs.find { |conf| conf.seq == seq }

    if source && !source.new_record?
      current_conf ||= source.try(data_form_association_name).find_by(form_conf_seq: form_conf_seq)&.data_form
    end

    return (raise Error::BaseError.new(message: 'form_conf 未找到')) unless current_conf.present?

    current_conf.create_or_update_data_form(
      find_attributes: find_attributes,
      extra_attributes: extra_attributes,
      source: source,
      data_form_association_name: data_form_association_name,
      &save_block
    )
  end

   # payload: model_key => value
  # payload_summary: model_key => value，经过精简
  # 中文 => value
  # 中文 => value，经过精简

  # trans: 是否需要转换为模型定义的名称（中文）
  # model_key: 是否需要过滤获取重新定义过map_key值
  # summary: 是否需要过滤只获取summary为true的
  def payload_value(payload = {}, trans: false, model_key: false, summary: false, dot_key: false)
    confs.reduce({}) do |out, conf|
      out.merge(
        conf.form.payload_value(
          payload,
          trans: trans,
          model_key: model_key,
          summary: summary,
          dot_key: dot_key
        )
      )
    end
  end

  class << self
    def init
      new({ setting: { confs: [], type: 'copy' } })
    end

    def init_form_form(form)
      new({
        setting: {
          confs: [
            {
              conf: {
                type: 'copy',
                form: form,
              }
            }
          ],
          type: 'copy'
        }
      })
    end
  end
end
