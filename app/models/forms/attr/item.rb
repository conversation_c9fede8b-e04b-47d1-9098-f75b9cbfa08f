class Forms::Attr::Item
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  FILE_FIELD_TYPES = %w[
    file
    image_single
    image_picker
    video_single
    image
  ].freeze

  attr_json :type, :string
  attr_json :name, :string
  attr_json :model_key, :string
  attr_json :model_key_configuration, Forms::Attr::ModelKeyConfiguration.to_type, array: true
  attr_json :model_key_prefix, :string
  attr_json :key, :string
  attr_json :model, Forms::Attr::ItemModel.to_type
  attr_json :rules, Forms::Attr::Rule.to_type, array: true
  attr_json :options, ActiveModel::Type::Value.new
  attr_json :conditions, Forms::Attr::Condition.to_type, array: true
  attr_json :fields, Forms::Attr::Item.to_type, array: true
  attr_json :index_attributes, Forms::Attr::IndexAttribute.to_type, array: true
  attr_json :column_attributes, Forms::Attr::ColumnAttribute.to_type, array: true
  attr_json :actions, Forms::Attr::Action.to_type, array: true
  attr_json :collection_stat_condition, Com::Attr::Stat::Collection.to_type
  attr_json :resource_stat_condition, Com::Attr::Stat::Resource.to_type
  attr_json :detail_resource_stat_condition, Com::Attr::Stat::Resource.to_type

  def serializable_hash(*args)
    super.merge(
      form_empty: form_empty?
    )
  end

  def present?
    super() && (fields.present? || column_attributes.present?)
  end

  def blank?
    super() || (fields.blank? && column_attributes.blank?)
  end

  def form_empty?
    blank?
  end

  def dot_model_key
    [model_key_prefix, model_key].reject(&:blank?).join('.')
  end

  # 递归获取flag
  def find_by_flag(flag)
    flag_model_key = "#{flag}_payload"
    return self if model_key == flag_model_key

    fields.each do |field|
      _form = field.find_by_flag flag
      return _form if _form
    end

    nil
  end

  attr_accessor :children, :parent_field

  # 从fields里面过滤出来第一层有model的fields
  def model_fields
    case type&.to_sym
    when :layout, :key_layout, :container_layout
      (fields || []).map(&:model_fields).flatten.compact
    when :condition
      conditions.map do |condition|
        (condition.fields || []).map(&:model_fields)
      end.flatten.compact
    # list 保持嵌套，使用 children 作为 key
    when :list
      self.children = (fields || []).map(&:model_fields).compact.reduce(:concat)
      [self]
    when nil
      []
    else
      if type&.to_s.include?('::') && fields.present?
        (fields || []).map(&:model_fields).flatten.compact
      elsif model_key_configuration.present?
        # 如果有model_key_configuration，则拆分为多个
        model_key_configuration.map do |configuration|
          self.class.new({
            **as_json.symbolize_keys,
            model_key: configuration.model_key,
            key: configuration.key,
          })
        end
      else
        [self]
      end
    end
  end

  # payload: model_key => value
  # payload_summary: model_key => value，经过精简
  # 中文 => value
  # 中文 => value，经过精简

  # trans: 是否需要转换为模型定义的名称（中文）
  # model_key: 是否需要过滤获取重新定义过map_key值
  # summary: 是否需要过滤只获取summary为true的
  def payload_value(payload = {}, trans: false, model_key: false, summary: false, dot_key: false)
    _payload = payload.deep_dup&.with_indifferent_access || {}
    _payload = _payload.to_dot_hash
    if summary
      summary_field_keys = model_fields.map do |field|
        field.model&.summary ? field.dot_model_key : nil
      end.compact
      _payload.slice!(*summary_field_keys)
    end

    if model_key
      model_field_keys = model_fields.map do |field|
        field.model_key != field.key ? field.dot_model_key : nil
      end.compact
      _payload.slice!(*model_field_keys)
    end

    if trans
      _payload = model_fields.each_with_object({}) do |field, out|
        value = _payload[field.dot_model_key]
        out[field.name] = value if value.present?
      end
    end

    dot_key.present? ? _payload.with_indifferent_access : _payload.to_nested_hash.with_indifferent_access
  end

  # 导出的列
  def export_header_attrs(**args)
    process_field_proc = proc do |field|
      next if field.options['viewHidden'] == true

      attr_type = field.model&.attr_type.to_s == 'date' ? field.type : field.model&.attr_type
      hash = { key: field.model_key, name: field.name, attr_type: attr_type }
      hash.merge!(format: :string) if field.model&.attr_type.to_s == 'string'
      hash.merge!(attr_type: :file) if field.type.to_s.in?(FILE_FIELD_TYPES)
      hash.merge!(json: field.model_key_prefix) if field.model_key_prefix.present?
      hash.merge!(select: field.options['select']) if field&.options && field.options['select'].present?
      hash.merge!(
        children: (field.children || []).map(&process_field_proc).compact.map do |h|
          # 嵌套由嵌套取值取出
          h[:prefix] = ''
          h
        end,
      )
      hash.merge!(args)
      hash
    end
    headers = model_fields.map(&process_field_proc).compact

    if column_attributes.present?
      column_attributes.select do |column_attribute|
        column_attribute.export&.dig('on') != false
      end.map do |column_attribute|
        {
          key: column_attribute.dataIndex,
          name: column_attribute.title.is_a?(Array) ? column_attribute.title.first : column_attribute.title,
        }.merge(
          headers.find { |header| [header[:json], header[:key]].compact.join('.') == column_attribute.dataIndex } || {},
        ).merge(args)
      end
    else
      headers
    end
  end

  def import_header_attrs(**args)
    process_field_proc = proc do |field|
      # next if field.options['viewHidden'] == true
      next if field.options['formHidden'] == true || field.options['formDisabled'] == true

      attr_type = field.model&.attr_type.to_s == 'date' ? field.type : field.model&.attr_type
      hash = { key: field.model_key, name: field.name, attr_type: attr_type }
      hash.merge!(format: :string) if field.model&.attr_type.to_s == 'string'
      hash.merge!(attr_type: :file) if field.type.to_s.in?(FILE_FIELD_TYPES)
      hash.merge!(json: field.model_key_prefix) if field.model_key_prefix.present?
      hash.merge!(select: field.options['select']) if field&.options && field.options['select'].present?
      hash.merge!(
        children: (field.children || []).map(&process_field_proc).compact.map do |h|
          # 嵌套由嵌套取值取出
          h[:prefix] = ''
          h
        end,
      )
      hash.merge!(args)
      hash
    end

    headers = model_fields.map(&process_field_proc).compact

    if column_attributes.present?
      column_attributes.select do |column_attribute|
        column_attribute.import&.dig('on') != false
        # column_attribute.import&.dig('on') || column_attribute.import&.dig(:on)
      end.map do |column_attribute|
        {
          key: column_attribute.dataIndex,
          name: column_attribute.title.is_a?(Array) ? column_attribute.title.first : column_attribute.title,
        }.merge(
          headers.find { |header| [header[:json], header[:key]].compact.join('.') == column_attribute.dataIndex } || {},
        ).merge(args)
      end
    else
      headers
    end
  end

  # 根据表单配置，提取对象record的数据
  def form_json(record, mode: 'simple')
    # 如果有resource_stat_condition，则先根据条件进行计算
    # 后面根据需要再抽取其中的值
    case mode.to_s
    when 'single', 'simple'
      ta_statistic = resource_stat_condition.present? ? record.ta_statistic(resource_stat_condition) : {}
      keys = []
      keys.concat(column_attributes&.map(&:dataIndex))
      extract_record_form_json(record, keys, ta_statistic: ta_statistic)
    when 'detail'
      stat_condition = detail_resource_stat_condition.present? ?
        detail_resource_stat_condition : resource_stat_condition
      ta_statistic = stat_condition.present? ? record.ta_statistic(resource_stat_condition) : {}
      keys = []
      keys.concat(column_attributes&.map(&:dataIndex))
      keys.concat(model_fields&.map(&:dot_model_key))
      keys = keys.compact.uniq
      extract_record_form_json(record, keys, ta_statistic: ta_statistic)
    end
  end

  def extract_record_form_json(record, keys, ta_statistic: {})
    h = {}
    Array.wrap(keys).each do |key|
      key = key.to_s
      h[key] =
        if key == 'ta_statistic'
          ta_statistic
        elsif key.to_s.split('.').first == 'ta_statistic'
          try_method(ta_statistic, key.sub('ta_statistic.', ''))
        else
          try_method(record, key)
        end
    end

    h.to_nested_hash
  end

  def self.create_by_attribute(attribute_key, attribute_define)
    attribute_info = attribute_define.last
    attribute_type = ActiveModel::Type::Value.cast_type_value attribute_define.first
    attribute_name = attribute_type.comment || attribute_key
    options = { span: 24 }

    return nil if attribute_type.type.blank?

    case attribute_type.type.to_sym
    when :string
      if attribute_type.respond_to?(:mapping, true)
        # Enum
        mapping = attribute_type.send(:mapping)
        options.merge!(
          select: mapping.map { |value, label| { label: label, value: value } },
        )
        component_type = 'select'
      else
        component_type = 'input'
      end
    when :integer
      component_type = 'number'
    when :float
      component_type = 'number'
    when :decimal
      component_type = 'number'
    when :text
      component_type = 'textarea'
    when 'date'
      component_type = 'date'
    when 'datetime'
      component_type = 'datetime'
    when :json, :jsonb
      component_type = 'key_layout'
    else
      component_type = attribute_type.type
    end

    key = "#{component_type}_#{rand(1..100_000_000_000)}"

    new(
      type: component_type,
      name: attribute_name,
      model_key: attribute_key,
      key: key,
      model: { attr_type: attribute_type.type },
      options: options,
      fields: [],
    )
  end

  def find_fields_by_types(*field_types)
    field_types.map!(&:to_s)
    result_fields = []
    result_fields << self if type.to_s.in?(field_types)

    if fields.present?
      fields.each do |field|
        result_fields.concat field.find_fields_by_types(*field_types)
      end
    end

    if conditions.present?
      conditions.each do |condition|
        condition.fields.each do |field|
          result_fields.concat field.find_fields_by_types(*field_types)
        end
      end
    end

    result_fields.compact
  end

  def find_field_by_model_key(model_key, model_key_prefix: nil)
    find_result = model_key_prefix.present? ?
      model_key.to_s == self.model_key.to_s && model_key_prefix.to_s == self.model_key_prefix.to_s :
      model_key.to_s == self.model_key.to_s

    return self if find_result

    fields&.each do |field|
      find_field = field.find_field_by_model_key model_key, model_key_prefix: model_key_prefix
      return find_field if find_field
    end

    conditions&.each do |condition|
      condition.fields.each do |field|
        find_field = field.find_field_by_model_key model_key, model_key_prefix: model_key_prefix
        return find_field if find_field
      end
    end

    # 如果查找到最后，就是没有找到
    nil
  end

  def chain
    [*(parent_field&.chain || []).compact, self]
  end

  def each_field(parent = nil, &block)
    @parent_field = parent
    yield({
      field: self,
      chain: chain,
    })

    fields.each do |field|
      field.each_field(self, &block)
    end

    conditions&.each do |condition|
      condition.fields.each do |field|
        field.each_field(self, &block)
      end
    end
  end

  def self.caculation(item, value, &block)
    dig_hash = value&.to_dot_hash
    case item.type
    when 'list'
      (dig_hash&.dig(item.dot_model_key) || []).each(&block)
    when 'condition', 'layout', 'container_layout', 'key_layout'
      yield value
    else
      yield dig_hash&.dig(item.dot_model_key)
    end
  end

  def try_chain(payload, arr)
    arr.reduce(payload) do |r, m|
      r.try(m) || r.try(:[], m) || r.try(:[], m.to_sym)
    end
  end

  def try_method(payload, method)
    arr = method.to_s.split(/\./)
    try_chain(payload, arr)
  end

  def self.append_model_key_prefix form, append_value
    result = form.dup
    result.each_field do |item|
      chain_except_self = item[:chain]
      chain_except_self.pop
      next if chain_except_self.select { |f| f.type.in?(['list', 'nested_attributes']) }.count > 0
      item[:field].model_key_prefix = [append_value, form.model_key_prefix].compact.join('.')
      if item[:field].type == 'condition'
        item[:field].conditions.each do |condition|
          condition.model_key = [append_value, condition.model_key].compact.join('.')
        end
      end
    end
    result
  end
end
