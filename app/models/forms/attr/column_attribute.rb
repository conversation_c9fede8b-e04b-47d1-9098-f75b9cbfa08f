class Forms::Attr::ColumnAttribute
  include AttrJson::Model
  attr_json :_id, :string
  attr_json :align, :string
  attr_json :ellipsis, :boolean
  attr_json :color, :string
  attr_json :colSpan, :integer
  attr_json :dataIndex, :string
  attr_json :defaultFilteredValue, :string, array: true
  attr_json :defaultSortOrder, :string
  attr_json :filterDropdownVisible, :boolean
  attr_json :filtered, :boolean
  attr_json :filteredValue, :string, array: true
  attr_json :filterMultiple, :boolean
  attr_json :filters, ActiveModel::Type::Value.new, array: true
  attr_json :fixed, ActiveModel::Type::Value.new
  attr_json :key, :string
  attr_json :render, :string
  attr_json :customRender, ActiveModel::Type::Value.new
  attr_json :sorter, :boolean
  attr_json :sortOrder, ActiveModel::Type::Value.new
  attr_json :sortDirections, ActiveModel::Type::Value.new, array: true
  attr_json :title, :string, array: true
  attr_json :width, ActiveModel::Type::Value.new
  attr_json :slots, ActiveModel::Type::Value.new
  # custom
  attr_json :filteredDetails, ActiveModel::Type::Value.new, array: true
  # index.on，代表是否支持列表
  attr_json :index, ActiveModel::Type::Value.new # Forms::Attr::Export
  # export.on，代表是否支持导出
  attr_json :export, ActiveModel::Type::Value.new # Forms::Attr::Export
  attr_json :import, ActiveModel::Type::Value.new
  attr_json :table_column, ActiveModel::Type::Value.new
end
