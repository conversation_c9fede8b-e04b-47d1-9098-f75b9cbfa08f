class Forms::Attr::Conf::ModelSettingRef
  # 表单引用
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  # klass_singular#model_setting
  attr_json :model_setting_identity, :string
  attr_json :conf_seq, :string
  attr_json :setable_type, :string
  attr_json :setable_id, :string

  def serializable_hash(*args)
    super.merge(
      form: form,
    )
  end

  def self.name
    'model_setting'
  end

  def form_empty?
    form&.form_empty?
  end

  def form
    @form ||= get_form
  end

  def get_form
    klass_singular, flag = model_setting_identity.to_s.split('#')
    model_setting = ModelDefine.model_setting_by_singular_name(
      klass_singular,
      flag: flag,
      setable_type: setable_type,
      setable_id: setable_id,
    )

    unless model_setting
      return ModelSetting.template_by_singular_name(
        klass_singular,
        flag: flag,
      )&.form
    end

    form_setting = model_setting&.form_setting
    return nil if form_setting&.setting&.confs.blank?

    form_conf = conf_seq.present? ?
      form_setting.setting.confs.find { |conf| conf.seq == conf_seq } :
      form_setting.setting.confs.first
    form_conf&.form
  end
end
