class Forms::Attr::DataFormActionConf
  # include AttrJson::Model

  # attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

  # attr_json :form_conf_seq, :string
  # attr_json :action, Com::Attr::Action.to_type

  # def get_data_form(source:, **_opts)
  #   @data_form ||= form_conf_seq && source.find_data_form_by_seq(form_conf_seq)
  # end

  # def invode(source:, **opts)
  #   action.invoke(
  #     source: source,
  #     data_form: get_data_form(source: source, **opts),
  #     payload: get_data_form(source: source, **opts)&.payload || {},
  #     **opts
  #   )
  # end

  # def can_invoke?(source:, **opts)
  #   action.can_invoke?(
  #     source: source,
  #     data_form: get_data_form(source: source, **opts),
  #     payload: get_data_form(source: source, **opts)&.payload || {},
  #     **opts
  #   )
  # end
end
