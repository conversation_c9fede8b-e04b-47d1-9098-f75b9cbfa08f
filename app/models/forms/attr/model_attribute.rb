class Forms::Attr::ModelAttribute
  include AttrJson::Model
  attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

  attr_json :name, :string
  attr_json :key, :string
  attr_json :cast_type, :string, default: 'string'
  attr_json :array, :boolean, default: false
  # 抽取方法
  attr_json :chain, ActiveModel::Type::Value.new

  def extract_value(record, *args)
    value_chain = chain.presence || key
    Com::Invoker.invoke_chain(record, value_chain)
  end
end
