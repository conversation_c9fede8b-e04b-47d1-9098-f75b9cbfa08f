module Forms::Model::Template
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app, optional: true
    has_one :model_setting, foreign_key: :forms_template_id, dependent: :nullify

    attribute :uuid, :string, comment: '表单的唯一标识，可以替代id给前端使用', index: true
    attribute :name, :string, comment: '表单的名称'
    attribute :form, :jsonb, comment: '表单配置的内容', default: {}
    serialize :form, coder: Forms::Attr::Item.to_serialization_coder
    attribute :form_setting, :jsonb, comment: '表单内容'
    serialize :form_setting, coder: Forms::Attr::FormSetting.to_serialization_coder

    before_save :set_uuid
  end

  def form=(val)
    form_val = form.as_json.present? ? form.as_json.merge(val) : val
    super(form_val)
  end

  def clone(clone_name = nil)
    clone_name ||= "#{name} 复制"
    form_template = deep_clone except: [:name, :uuid]
    form_template.name = clone_name
    form_template.save!
    form_template
  end

  def form_json(record, mode: 'simple')
    form&.form_json(record, mode: mode)
  end

  private

  def set_uuid
    self.uuid ||= UUIDTools::UUID.timestamp_create
  end

  class_methods do
  end
end
