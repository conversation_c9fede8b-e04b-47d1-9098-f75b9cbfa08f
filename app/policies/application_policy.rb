class ApplicationPolicy
  attr_reader :user, :record, :context, :collection, :klass

  def initialize(user, record)
    @user = user
    if record.is_a?(Hash)
      hash = record.with_indifferent_access
      @record = hash[:record]
      @context = hash[:context] || {}
      @collection = hash[:collection]
      @klass = hash[:klass]

      hash[:parents]&.each do |parent_sym, value|
        parent_sym.to_sym.in?([:record, :user]) ?
          instance_variable_set("@parent_#{parent_sym}".to_sym, value) :
          instance_variable_set("@#{parent_sym.to_sym}", value)
      end
    else
      @record = record
    end
  end

  class Scope
    attr_reader :user, :scope

    def initialize(user, scope, **parents)
      @user = user
      @scope = scope

      parents&.each do |parent_sym, value|
        parent_sym.to_sym.in?([:record, :user]) ?
          instance_variable_set("@parent_#{parent_sym}".to_sym, value) :
          instance_variable_set("@#{parent_sym.to_sym}", value)
      end
    end

    def resolve
      scope.all
    end
  end
end
