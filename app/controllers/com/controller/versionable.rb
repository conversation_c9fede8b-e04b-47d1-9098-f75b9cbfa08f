module Com::Controller::Versionable
  extend ActiveSupport::Concern

  included do
    def version_relationships_index
      @version_relationships = ransack_paginate(resource.version_relationships)

      respond_with @version_relationships, template: 'version_relationships/index', status: 200
    end

    def version_relationships_show
      @version_relationship = resource.version_relationships.find(params[:version_relationship_id])
      respond_with @version_relationship, template: 'version_relationships/show', status: 200
    end

    def paper_trail_versions_index
      @paper_trail_versions = ransack_paginate(resource.versions)

      respond_with @paper_trail_versions, template: 'paper_trail_versions/index', status: 200
    end

    def paper_trail_versions_show
      @paper_trail_versions = resource.versions.find(params[:version_id])
      respond_with @paper_trail_versions, template: 'paper_trail_versions/show', status: 200
    end
  end

  class_methods do
  end
end