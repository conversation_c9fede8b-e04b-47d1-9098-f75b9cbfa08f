module Com::Controller::TaStatistic
  extend ActiveSupport::Concern

  included do
    # 搜索的参数结构如下：
    # key: 'xxx'
    # filter: { name_eq: 'test' }
    # group: { attrs: [], enum_dics: {} }
    # stat: { method: 'group_sum', attrs: [], include_count: false, include_key: false }
    # jsonata: { expression: '' }
    def ta_collection_statistic
      stat_condition = Com::Attr::Stat::Collection.new params.to_unsafe_h[:stat_condition]
      stat_result  = collection.ta_statistic(stat_condition)
      render json: stat_result, status: 201
    end

    def ta_resource_statistic
      stat_condition = Com::Attr::Stat::Resource.new params.to_unsafe_h[:stat_condition]
      stat_result = resource.ta_statistic(stat_condition)
      render json: stat_result, status: 201
    end
  end

  class_methods do
  end
end
