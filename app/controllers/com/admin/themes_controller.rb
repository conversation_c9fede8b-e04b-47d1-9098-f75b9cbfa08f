class Com::Admin::ThemesController < SimpleController::BaseController
  defaults(
    resource_class: Com::Theme,
    collection_name: 'themes',
    instance_name: 'theme',
    view_path: 'com/themes',
  )
  auth_action :user
  permit_action :com_admin

  protected

  def	begin_of_association_chain
    current_user.app
  end

  private

  def theme_params
    params.require(:theme).permit(
      :name,
      :model_flag,
      model_payload: {},
      conf: {},
    )
  end
end
