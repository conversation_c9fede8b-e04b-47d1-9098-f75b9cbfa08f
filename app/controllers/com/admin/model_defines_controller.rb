class Com::Admin::ModelDefinesController < SimpleController::BaseController
  defaults(
    resource_class: ModelDefine,
    collection_name: 'model_defines',
    instance_name: 'model_define',
    view_path: 'model_defines'
  )
  auth_action :user
  permit_action :com_admin

  private
    def model_define_params
      params.require(:model_define).permit(
        :klass, :name, association_chain: []
      )
    end
end
