class Com::Admin::VersionHistoriesController < SimpleController::BaseController
  defaults(
    resource_class: Com::VersionHistory,
    collection_name: 'version_histories',
    instance_name: 'version_history',
    view_path: 'com/version_histories',
  )
  auth_action :user
  permit_action :com_admin

  protected

  def	begin_of_association_chain
    current_user.app
  end

  def	method_for_association_chain
    :version_histories
  end

  private

  def create_version_history_params
    update_version_history_params.merge(
      creator: current_user
    )
  end

  def update_version_history_params
    params.require(:version_history).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :name,
      :version,
      :position,
      model_payload: {},
      content: {},
    )
  end
end
