class Com::Admin::ApiSettingsController < SimpleController::BaseController
  defaults(
    resource_class: ApiSetting,
    collection_name: 'api_settings',
    instance_name: 'api_setting',
    view_path: 'api_settings',
  )
  auth_action :user
  permit_action :com_admin

  private

  def api_setting_params
    params.require(:api_setting).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_define_id,
      :app_id,
      :klass,
      :action,
      :uid,
      extract_conf: {},
    )
  end
end
