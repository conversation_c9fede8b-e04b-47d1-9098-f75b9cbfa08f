class Com::Admin::ModelConfsController < SimpleController::BaseController
  defaults(
    resource_class: ModelConf,
    collection_name: 'model_confs',
    instance_name: 'model_conf',
    view_path: 'model_confs',
  )
  auth_action :user
  permit_action :com_admin

  private

  def model_conf_params
    params.require(:model_conf).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_define_id,
      :name,
      :klass,
      conf: {},
    )
  end
end
