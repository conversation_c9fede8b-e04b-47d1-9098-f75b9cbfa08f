class Com::Admin::ComponentSettingsController < SimpleController::BaseController
  defaults(
    resource_class: ComponentSetting,
    collection_name: 'component_settings',
    instance_name: 'component_setting',
    view_path: 'component_settings',
  )
  auth_action :user
  permit_action :com_admin

  protected

  def	after_association_chain association
    association.where(app_id: [nil, current_user.app_id])
  end

  private

  def component_setting_params
    params.require(:component_setting).permit(
      *resource_class.try(:extra_permitted_attributes),
      :app_id,
      :seq,
      :model_flag,
      :name,
      :component_klass,
      :component_path,
      model_payload: {},
      conf: {},
    )
  end
end
