class Com::Admin::DataFormsController < SimpleController::BaseController
  defaults(
    resource_class: DataForm,
    collection_name: 'data_forms',
    instance_name: 'data_form',
    view_path: 'data_forms',
  )
  auth_action :user
  permit_action :com_admin

  protected

  def	begin_of_association_chain
    current_app
  end

  private

  def data_form_params
    params.require(:data_form).permit(
      *resource_class.try(:extra_permitted_attributes),
      :create_user_id,
      :source_type,
      :source_id,
      :record_type,
      :record_id,
      :type,
      :model_flag,
      :source_flag,
      :state,
      model_payload: {},
      model_detail: {},
      payload: {},
      summary: {},
      form_conf: {},
      options: {},
      meta: {},
    )
  end
end
