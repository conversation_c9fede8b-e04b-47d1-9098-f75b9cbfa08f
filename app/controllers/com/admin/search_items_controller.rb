class Com::Admin::SearchItemsController < SimpleController::BaseController
  defaults(
    resource_class: Com::SearchItem,
    collection_name: 'search_items',
    instance_name: 'search_item',
    view_path: 'com/search_items',
  )
  auth_action :user
  permit_action :com_admin

  protected

  def	begin_of_association_chain
    current_user.app
  end

  private

  def search_item_params
    params.require(:search_item).permit(
      :name,
      :position,
      :enabled,
      :model_flag,
      :group_name,
      conditions: {},
      model_payload: {},
    )
  end
end
