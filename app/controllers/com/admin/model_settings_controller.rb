class Com::Admin::ModelSettingsController < SimpleController::BaseController
  defaults(
    resource_class: ModelSetting,
    collection_name: 'model_settings',
    instance_name: 'model_setting',
    view_path: 'model_settings',
  )
  auth_action :user
  permit_action :com_admin

  belongs_to :model_define, shallow: true

  protected

  def after_association_chain(association)
    association.where(app_id: [nil, current_user.app_id])
  end

  private

  def model_setting_params
    params.require(:model_setting).permit(
      *resource_class.try(:extra_permitted_attributes),
      :setable_type,
      :setable_id,
      :app_id,
      :forms_template_id,
      :flag,
      :flag_name,
      form: {},
      form_setting: {},
      i18n_form_setting: {},
    )
  end
end
