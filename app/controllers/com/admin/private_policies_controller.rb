class Com::Admin::PrivatePoliciesController < SimpleController::BaseController
  defaults(
    resource_class: Com::PrivatePolicy,
    collection_name: 'private_policies',
    instance_name: 'private_policy',
    view_path: 'com/private_policies',
  )
  auth_action :user
  permit_action :com_admin

  protected

  def	begin_of_association_chain
    current_user.app
  end

  def	method_for_association_chain
    :private_policies
  end

  def	after_association_chain association
    association.order(position: :desc)
  end

  private

  def private_policy_params
    params.require(:private_policy).permit(
      :name,
      :key,
      :position,
      content: {},
    )
  end
end
