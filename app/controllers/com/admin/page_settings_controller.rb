class Com::Admin::PageSettingsController < SimpleController::BaseController
  defaults(
    resource_class: PageSetting,
    collection_name: 'page_settings',
    instance_name: 'page_setting',
    view_path: 'page_settings',
  )
  auth_action :user
  permit_action :com_admin

  protected

  def	after_association_chain(association)
    association.where(app_id: [nil, current_user.app_id])
  end

  private

  def page_setting_params
    conf = params.require(:page_setting).fetch(:conf, {}).permit!
    params.require(:page_setting).permit(
      *resource_class.try(:extra_permitted_attributes),
      :app_id,
      :seq,
      :model_flag,
      :name,
      :render_html,
      model_payload: {},
    ).merge(
      conf.present? ? { conf: conf } : {},
    )
  end
end
