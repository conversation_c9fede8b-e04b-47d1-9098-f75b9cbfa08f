class Com::User::SearchItemsController < SimpleController::BaseController
  defaults(
    resource_class: Com::SearchItem,
    collection_name: 'search_items',
    instance_name: 'search_item',
    view_path: 'com/search_items',
  )
  auth_action :user, skip_error: true

  def	begin_of_association_chain
    @current_user&.app || App.first
  end

  def	after_association_chain association
    association.where.not(enabled: false)
  end

end
