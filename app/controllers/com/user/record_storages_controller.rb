class Com::User::RecordStoragesController < SimpleController::BaseController
  defaults(
    resource_class: Com::RecordStorage,
    collection_name: 'record_storages',
    instance_name: 'record_storage',
    view_path: 'com/record_storages',
  )
  auth_action :user

  def create
    @record_storage = current_user.record_storages.where(
      key: record_storage_key_params[:key],
    ).first_or_initialize
    @record_storage.assign_attributes(*record_storage_params)
    @record_storage.save

    respond_resource
  end

  def fetch
    @record_storage = current_user.record_storages.where(
      key: record_storage_key_params[:key],
    ).first_or_initialize

    respond_resource
  end

  def clear
    current_user.record_storages.where(
      key: record_storage_key_params[:key],
    ).destroy_all
    head 201
  end

  private

  def user_record_storage
    current_user.record_storages.where(
      key: record_storage_key_params[:key],
    ).first
  end

  def record_storage_key_params
    params.require(:record_storage).permit(
      :key,
    )
  end

  def record_storage_params
    params.require(:record_storage).permit(
      storage: {},
    )
  end
end
