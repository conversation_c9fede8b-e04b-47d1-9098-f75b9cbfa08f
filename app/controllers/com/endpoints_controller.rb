# frozen_string_literal: true

class Com::EndpointsController < SimpleController::BaseController
  auth_action :user

  # 通过keys获取redis中的值
  def fetch_data_by_keys
    keys = params.require(:keys).is_a?(String) ? params[:keys].split(',') : params[:keys].map(&:to_s)

    records = $broadcast_redis.mget(*keys).each_with_object({}) do |value, result|
      result[keys.shift] = begin
                             JSON.parse(value || '{}')
                           rescue JSON::ParserError
                             {}
                           end
    end

    render json: records
  end

end
