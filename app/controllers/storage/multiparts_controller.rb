class Storage::MultipartsController < ApplicationController
  auth_action :user, skip_error: true

  def create
    type     = params[:type]
    filename = params[:filename]
    prefix = params[:prefix] || current_user&.app&.code || App.first.code

    # 校验文件格式
    RailsCom::ActiveStorage::FileType.check_mime_type!(filename, content_type: type)

    key = SecureRandom.hex + File.extname(filename.to_s)
    key = [prefix, Time.now.year, key].compact.join('/').downcase

    options = {}
    options[:content_type]        = type if type
    # options[:content_disposition] = ContentDisposition.inline(filename) if filename
    options[:content_disposition] = ContentDisposition.inline(key)
    options[:acl]                 = 'public-read' if params[:public]

    result = client_call(:create_multipart_upload, key: key, **options)

    render json: { uploadId: result.fetch(:upload_id), key: result.fetch(:key) }, status: 201
  end

  def list
    key = param!('key')
    upload_id = param!('upload_id')

    result = client_call(:list_parts, upload_id: upload_id, key: key)

    res = result.map do |part|
      { PartNumber: part.fetch(:part_number), Size: part.fetch(:size), ETag: part.fetch(:etag) }
    end

    render json: res, status: 200
  end

  def part
    key = param!('key')
    upload_id = param!('upload_id')
    part_number = param!('part_number')

    result = client_call(:prepare_upload_part, upload_id: upload_id, key: key, part_number: part_number)

    render json: { url: result.fetch(:url) }, status: 200
  end

  def complete
    key   = param!('key')
    parts = param!('parts')
    upload_id = param!('upload_id')

    parts = parts.map do |part|
      { part_number: part.fetch('PartNumber'), etag: part.fetch('ETag') }
    rescue KeyError
      error! 'At least one part is missing "PartNumber" or "ETag" field'
    end

    client_call(:complete_multipart_upload, upload_id: upload_id, key: key, parts: parts)

    object_url = client_call(:object_url, key: key) # , public: opts[:public])

    render json: { location: object_url }, status: 201
  end

  def destroy
    key = param!('key')
    upload_id = param!('upload_id')

    begin
      client_call(:abort_multipart_upload, upload_id: upload_id, key: key)
    rescue Aws::S3::Errors::NoSuchUpload
      error!(
        "Upload doesn't exist for \"key\" parameter",
        status: 404,
      )
    end
    head 204
  end

  private

  def client_call(operation, **options)
    client = $s3_client

    client.send(operation, **options)
  end

  def param!(name)
    value = params[name]

    error! "Missing \"#{name}\" parameter" if value.nil?

    value
  end

  def error!(message, status: 400)
    render json: { error: message }, status: status
  end
end
