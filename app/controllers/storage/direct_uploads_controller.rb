class Storage::DirectUploadsController < ActiveStorage::DirectUploadsController
  skip_before_action :verify_authenticity_token

  include ActionController::AuthAction
  auth_action :user, skip_error: true

  def create
    filename = blob_args[:filename]
    content_type = blob_args[:content_type]
    app_code = blob_args[:app_code] || current_user&.app&.code || App.first.code

    # 校验文件格式
    RailsCom::ActiveStorage::FileType.check_mime_type!(filename, content_type: content_type)

    key = SecureRandom.hex + File.extname(filename.to_s)
    key = [app_code, Time.now.year, key].compact.join('/').downcase

    blob = ActiveStorage::Blob.create_before_direct_upload!(key: key, **blob_args)
    render json: direct_upload_json(blob)
  end

  private

  def blob_args
    params.require(:blob).permit(:app_code, :filename, :byte_size, :checksum, :content_type, metadata: {}).to_h.symbolize_keys
  end

  def direct_upload_json(blob)
    blob.as_json(
      root: false, methods: [:signed_id, :url],
    ).merge(
      direct_upload: {
        url: blob.service_url_for_direct_upload,
        headers: blob.service_headers_for_direct_upload,
      },
    )
  end
end
