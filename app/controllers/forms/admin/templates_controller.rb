class Forms::Admin::TemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Forms::Template,
    collection_name: 'templates',
    instance_name: 'template',
    view_path: 'forms/templates'
  )
  auth_action :user
  permit_action :com_admin

  def clone
    @template = resource.clone(params[:name])
    respond_with @template, template: "#{view_path}/show", status: 201
  end

  protected

  def after_association_chain association
    association.where(app_id: nil)
  end

  private

  def template_params
    params.require(:template).permit(
      :uuid, :name, form: {}, form_setting: {}
    )
  end
end
