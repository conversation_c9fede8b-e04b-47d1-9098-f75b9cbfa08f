class Forms::ResourceInfosController < SimpleController::BaseController
  auth_action :user

  # 通过基本权限，获取所有的权限处理
  def enable_actions
    routes = target_controller_routes
    result_actions = routes.map do |route|
      route.defaults[:action]
    end.uniq
    render json: { actions: result_actions }, status: 201
  end

  def collection_enable_actions
    routes = target_controller_routes mode: 'collection'
    result_actions = routes.map do |route|
      route.defaults[:action]
    end.uniq
    render json: { actions: result_actions }, status: 201
  end

  def member_enable_actions
    routes = target_controller_routes mode: 'member'
    result_actions = routes.map do |route|
      route.defaults[:action]
    end.uniq
    render json: { actions: result_actions }, status: 201
  end

  def find_by_ids
    _resource_class = target_controller.resource_class
    association = _resource_class.where(id: resource_info_params[:ids])

    if resource_info_params[:config].present?
      attr_config = resource_info_params[:config].to_h
      render json: { records: records.as_json(attr_config) }
    else
      records = association.map do |record|
        resource_info_params[:attrs].each_with_object({ id: record.id }) do |attr, h|
          h[attr] = record.try_method(attr)
        end
      end
      render json: { records: records }
    end
  end

  # 用 文件 交换信息
  def find_by_file
    target_controller_instance = target_controller.new
    target_controller_instance.action_name = :index
    target_controller_instance.request = request
    target_controller_instance.response = target_controller_instance.class.make_response!(request)
    begin
      target_controller_instance.process(:index)
    rescue StandardError
      nil
    end

    target_collection = target_controller_instance.collection

    xlsx_file = params[:file] || importable_class.import_excel_klass.new(params[:uid])
    resource_ids = importable_class.exchange_to_ids(xlsx_file, target_collection, **params.to_unsafe_h.symbolize_keys)

    result = target_collection.where(id: resource_ids).paginate(page: 1, per_page: 999_999)

    instance_variable_set("@#{target_controller_instance.send(:resource_collection_name)}", result)
    respond_with(result, { template: "#{target_controller_instance.class.view_path}/index" })
  rescue TalltyImportExport::Import::RecordNotFountError => e
    render json: { message: e.message, code: -1 }, status: 422
  end

  def upload_excel
    excel = TalltyImportExport::Excel.new
    excel.load(params[:file])
    render json: { uid: excel.uid }
  end

  def excel
    excel = TalltyImportExport::Excel.new(params[:uid])
    pagination = excel.records_pagination(page: params[:page] || 1, per_page: params[:per_page] || 15)
    render json: {
      current_page: pagination.current_page,
      total_pages: pagination.total_pages,
      total_count: pagination.count,
      titles: excel.titles,
      records: pagination,
    }
  end

  def import_headers
    form = Forms::Attr::Item.new(params[:ta_template].permit!)
    render json: { headers: form.import_header_attrs }
  end

  def export_import_template
    form = Forms::Attr::Item.new(params[:ta_template].permit!)
    headers = form.import_header_attrs
    import_instance = TalltyImportExport::Import.new(ActiveRecord::Base)
    path = import_instance.export_template_xlsx(headers: headers)
    send_file path
  end

  private

  def resource_info_params
    params.require(:resource_info).permit(
      :resource_id,
      :path,
      ids: [],
      config: {},
      context: {},
      attrs: [],
    )
  end

  def target_controller
    path = (params[:path] || resource_info_params[:path]).gsub(/\$\{.+\}/, '1')
    _controller = begin
      "#{resource_info_params[:path].camelize}Controller".constantize
    rescue StandardError
      nil
    end
    _controller ||= "#{Rails.application.routes.recognize_path(path)[:controller].camelize}Controller".constantize
    _controller
  end

  def importable_class
    name_ary = target_controller.importable_class.name.split('::')
    while name_ary.count > 1
      break if "::#{name_ary.join('::')}".safe_constantize.is_a?(ActiveRecord::Base)

      name_ary.shift
    end
    "::#{name_ary.join('::')}".safe_constantize
  end

  def target_controller_routes(mode: nil)
    controller_class = target_controller
    routes = RailsCom::ActionRoute.routes controller_class
    controller = controller_class.new
    controller.request = request
    action_callbacks = controller._process_action_callbacks

    controller.send(:auth_perform) if action_callbacks.map(&:filter).include?(:auth_perform)

    if defined?(Permit::Model::User) && action_callbacks.map(&:filter).include?(:check_action_authorize!) && controller.current_user
      unless controller.current_user.send(:skip_check_controller?, controller_class.to_s)
        controller.instance_variable_set(:@permit_handled, true) # 跳过 permit_audit
        permit_actions = controller.current_user.send(:permit_actions_with, controller: controller_class.to_s)
        routes.select! { |r| permit_actions.include? r.defaults[:action] }
      end
    end
    
    controller.send(:permit_perform) if action_callbacks.map(&:filter).include?(:permit_perform)
    routes.select! do |route|
      !%w[export_headers upload_excel excel import_headers exchange].include?(route.defaults[:action])
    end

    case mode.to_s
    when 'collection'
      routes.select! do |route|
        action = route.defaults[:action]
        is_collection = !route.required_parts&.include?(:id)
        is_policy = begin
          controller.send(
            :authorize_if_policy_class,
            {
              context: resource_info_params[:context],
              klass: controller.resource_class,
            },
            "#{action}?",
          ).present?
        rescue StandardError
          false
        end
        is_collection && is_policy
      end
    when 'member'
      routes.select! do |route|
        action = route.defaults[:action]
        is_member = route.required_parts&.include?(:id)
        is_policy = begin
          controller.send(
            :authorize_if_policy_class,
            {
              record: controller.resource_class.find(resource_info_params[:resource_id]),
              context: resource_info_params[:context],
              klass: controller.resource_class,
            },
            "#{action}?",
          ).present?
        rescue StandardError
          false
        end
        is_member && is_policy
      end
    else
      routes.select! do |route|
        action = route.defaults[:action]
        is_policy = begin
          controller.send(
            :authorize_if_policy_class,
            {
              context: resource_info_params[:context],
              klass: controller.resource_class,
            },
            "#{action}?",
          ).present?
        rescue StandardError
          false
        end
        is_policy
      end
    end
    routes
  rescue StandardError
    []
  end
end
