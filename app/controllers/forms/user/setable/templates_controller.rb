class Forms::User::Setable::TemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Forms::Template,
    collection_name: 'templates',
    instance_name: 'template',
    view_path: 'forms/templates',
  )
  auth_action :user

  def resource
    model_setting = ModelDefine.model_setting_by_singular_name(
      params[:model_singular_name],
      flag: params[:flag],
      app: current_user.app,
      setable_type: params[:setable_type],
      setable_id: params[:setable_id],
    )
    @template = model_setting.present? ?
      model_setting&.fake_template :
      ModelSetting.template_by_singular_name(
        params[:model_singular_name],
        flag: params[:flag] || 'model',
      )
    #
    # @template = ModelDefine.model_setting_by_singular_name(
    #   params[:model_singular_name],
    #   flag: params[:flag],
    #   app: current_user.app,
    # )&.forms_template
  end
end
