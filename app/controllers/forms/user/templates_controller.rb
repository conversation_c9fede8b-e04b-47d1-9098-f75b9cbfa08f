class Forms::User::TemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Forms::Template,
    collection_name: 'templates',
    instance_name: 'template',
    view_path: 'forms/templates/users'
  )

  # NOTE: uuid 代替 id 返回，给选择器使用

  auth_action :user

  protected

  def resource
    @template = end_of_association_chain.find_by!(uuid: params[:id])
  end

  def after_association_chain association
    association.where(app_id: [nil, current_user.app_id])
  end
end
