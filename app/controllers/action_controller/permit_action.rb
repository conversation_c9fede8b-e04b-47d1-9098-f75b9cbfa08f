module ActionController::PermitAction
  extend ActiveSupport::Concern

  included do
    private :permit_perform
  end

  def permit_perform(permit_auth: nil)
    return if @permit_handled # 如果 rails_permit 已处理就跳过

    permit_auth ||= current_auth
    unless permit_auth.has_any_role?(*self.class.permit_roles)
      raise Error::PermitError.new
    end
  rescue
    raise Error::PermitError.new
  end

  def permit_perform_with_creator
    return if @permit_handled # 如果 rails_permit 已处理就跳过

    has_role = current_auth&.respond_to?(:has_any_role?) && current_auth&.has_any_role?(*self.class.permit_roles)
    is_creator = (resource&.try(:creator) == current_auth rescue false)
    unless has_role || is_creator
      raise Error::PermitError.new
    end
  end

  module ClassMethods
    def permit_action *permit_roles, only: nil, &block
      if only
        before_action only: only do
          block && instance_eval(&block) || permit_perform
        end
      else
        before_action do
          block && instance_eval(&block) || permit_perform
        end
      end
      @permit_roles = permit_roles
    end

    def permit_action_with_creator *permit_roles, only: nil
      if only
        before_action :permit_perform_with_creator, only: only
      else
        before_action :permit_perform_with_creator
      end
      @permit_roles = permit_roles
    end

    def permit_roles
      @permit_roles || []
    end
  end
end
