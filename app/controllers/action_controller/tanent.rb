module ActionController::Tan<PERSON>
  extend ActiveSupport::Concern

  included do
    helper_method :current_tanent

    before_action :find_current_tanent
  end

  def current_tanent
    @current_tanent
  end

  def find_current_tanent
    return @current_tanent if @current_tanent.present?

    tenant_code = params[:tenant_code] || request.headers['HTTP_TANENT_CODE']
    @current_tanent = Tanent.find_by(code: tenant_code)
    Current.tanent = @current_tanent
    Current.tenant = @current_tanent
  end
end
