module ActionController::PunditPermission
  extend ActiveSupport::Concern

  class_methods do
    def pundit_action_permit(actions)
      full_cls_name = self.to_s
      namespaces = full_cls_name.split("::")
      parent_namespace = namespaces[0...namespaces.size - 1]
      cls_name = namespaces.last
      namespace_name = parent_namespace.join("::")
      policy_name = cls_name.sub(/Controller$/, 'DatabasePolicy')
      full_policy_name = namespace_name + "::" + policy_name
      policy_cls = full_policy_name.safe_constantize
      unless policy_cls
        policy_cls = Class.new(ApplicationPolicy)
        return unless namespace_name.safe_constantize
        namespace_name.safe_constantize.const_set(policy_name, policy_cls)
      end
      policy_cls.include(ActionController::PunditPermission::ActionPermit)
      policy_cls.enable_action_permission(full_cls_name, actions)
    end

    def pundit_data_permit
      full_cls_name = self.to_s
      namespaces = full_cls_name.split("::")
      parent_namespace = namespaces[0...namespaces.size - 1]
      cls_name = namespaces.last
      namespace_name = parent_namespace.join("::")
      policy_name = cls_name.sub(/Controller$/, 'DatabasePolicy')
      full_policy_name = namespace_name + "::" + policy_name
      scope_name = "#{full_policy_name}::Scope"
      scope_cls = scope_name.safe_constantize
      if scope_cls.nil? || scope_cls.name.to_s != scope_name
        policy_cls = full_policy_name.safe_constantize
        if policy_cls.nil?
          policy_cls = Class.new(ApplicationPolicy)
          return unless namespace_name.safe_constantize
          namespace_name.safe_constantize.const_set(policy_name, policy_cls)
        end
        scope_cls = Class.new(ApplicationPolicy::Scope)
        full_policy_name.safe_constantize.const_set('Scope', scope_cls)
        scope_cls = scope_name.safe_constantize
        scope_cls.instance_variable_set(:@current_klass, full_cls_name)
      end
      scope_cls.include(ActionController::PunditPermission::DataPermit)
    end

    private

    def undefine_instance_methods(klass)
      klass.instance_methods(false).each do |method_name|
        klass.send(:undef_method, method_name)
      end
    end
  end

  module ActionPermit
    extend ActiveSupport::Concern
    class_methods do
      def enable_action_permission(cls_name, only)
        permit_actions = Array(only).map(&:to_s)
        permit_actions.compact.uniq.each do |action|
          define_method "#{action}?" do
            can = user.send(:action_permissions)&.include?("#{cls_name}##{action}")
            return can unless can

            if record&.respond_to?("permit_#{action}?")
              return record.send("permit_#{action}?", user)
            end
            true
          end
        end
      end
    end
  end

  module DataPermit
    def resolve
      association = super
      tenant_dps = Current.tenant&.try(:data_scopes) || []
      user_dps = user&.try(:data_permissions) || []
      current_klass = self.class.instance_variable_get(:@current_klass)
      (tenant_dps + user_dps).uniq.select { |dp| dp.payload.dig('klass') == current_klass }.each do |dp|
        filter = dp.config&.fetch('filter')
        arg = { "dp_filter_with_#{filter}": dp.config&.fetch('rules', {}) }
        association = Com::Invoker.invoke_chain(association, arg)
      end
      association
    end
  end
end