module ActionController::AuthAction
  extend ActiveSupport::Concern

  included do
    class_attribute :auth_config, default: {
      skip_error: false,      # 是否跳过认证错误
      klass_syms: [],        # 认证类符号列表
      klasses: []            # 认证类列表
    }

    helper_method :current_auth, :current_tenant, :current_user, :current_member, :current_app
    private :auth_perform
  end

  # 从请求中获取认证Token
  def request_token
    request.env['HTTP_AUTHORIZATION'].to_s.sub(/^Token /, '')
  end

  # 重置所有认证状态
  def reset_auth_state
    @current_app = nil
    @current_user = nil
    @current_auth = nil
    @current_member = nil
    Current.user = nil
  end

  # 执行认证流程
  def auth_perform
    begin
      # 优先保证能获取到tenant
      tenant_code = params[:tenant_code] || request.headers['HTTP_TENANT_CODE'] || request.headers['HTTP_TANENT_CODE']
      @current_tenant = Tanent.find_by(code: tenant_code)
      @current_tanent = @current_tenant
      Current.tenant = @current_tanent
      Current.tanent = @current_tanent

      token = request_token
      @account_info = User.auth!(token).with_indifferent_access
      result = load_auth(@account_info)

      # 只在认证成功时执行自定义验证
      if result && respond_to?(:custom_auth_validation)
        begin
          custom_auth_validation
        rescue StandardError => e
          return handle_auth_error(e)
        end
      end

      result
    rescue StandardError => e
      handle_auth_error(e)
    end
  end

  # 加载认证信息
  def load_auth(account_info)
    return false if account_info.blank?

    klass_syms = self.class.auth_config[:klass_syms]
    app_id = account_info['app_id']
    user_id = account_info['user_id']
    account = account_info['account']

    begin
      reset_auth_state

      # 查找或设置当前应用
      app = App.find_by(code: app_id) || 
        App.find_by(code: request.env['HTTP_APPCODE']) || 
        App.first
      @current_app = app

      # 查找当前用户
      @current_user = if user_id.present?
                        app.users.effective.find_by(id: user_id)
                      else
                        app.users.effective.find_by(account: account)
                      end

      return false unless validate_user(@current_user)

      setup_current_user(account_info)
      @current_auth = determine_current_auth(klass_syms)

      return false unless validate_auth(@current_auth)
      true
    rescue StandardError => e
      handle_auth_error(e)
    end
  end

  private

  # 检查是否跳过错误处理
  def skip_error?
    self.class.auth_config[:skip_error]
  end

  # 验证用户状态
  def validate_user(user)
    return true if user.present? && !user.is_blocked

    message = if user.nil?
                '账号不存在或已失效'
              elsif user.is_blocked
                '账号已锁定'
              else
                '账号验证失败'
              end

    if skip_error?
      Rails.logger.error "Auth error (ignored): #{message}"
      false
    else
      raise Error::AuthError.new(message: message)
    end
  end

  # 验证认证对象
  def validate_auth(auth)
    return true if auth.present?

    message = '无效的认证类型'
    if skip_error?
      Rails.logger.error "Auth error (ignored): #{message}"
      false
    else
      raise Error::AuthError.new(message: message)
    end
  end

  # 设置当前用户信息
  def setup_current_user(account_info)
    return unless @current_user.present?

    @current_user.auth_id = account_info['id']
    @current_user.update(last_visit_at: Time.zone.now)

    # 设置当前成员（如果有）
    if request.env['HTTP_MEMBERID'].present?
      @current_member = @current_user.members.find_by_id(request.env['HTTP_MEMBERID'])
    end

    Current.user = @current_user
  end

  # 处理认证错误
  def handle_auth_error(error)
    reset_auth_state

    log_message = case error
                  when Error::TokenError
                    "Token验证失败: #{error.message}"
                  when Error::AuthError
                    error.message
                  else
                    Rails.logger.error "Authentication error: #{error.message}\n#{error.backtrace.join("\n")}"
                    '认证过程发生错误'
                  end

    if skip_error?
      @current_app = App.find_by(code: request.env['HTTP_APPCODE'])
      Rails.logger.error "Authentication error (ignored): #{log_message}"
      false
    else
      case error
      when Error::TokenError, Error::AuthError
        raise error
      else
        raise Error::AuthError.new(message: log_message)
      end
    end
  end

  # 确定当前认证对象
  def determine_current_auth(klass_syms)
    return @current_user if klass_syms.include?(:user)
    if klass_syms.include?(:member)
      @current_member = @current_user.members.first
      return @current_member
    end

    find_auth_by_class_types(klass_syms)
  end

  # 根据类型查找认证对象
  def find_auth_by_class_types(klass_syms)
    klasses = klass_syms.map { |klass_sym| klass_sym.to_s.classify.safe_constantize }.compact
    return nil if klasses.empty?

    klass_names = klasses.map(&:name)

    # 查找直接关联
    klasses.each do |klass|
      auth = klass.where(user: @current_user)&.first
      return auth if auth.present?
    end

    # 查找通过 member_identity 关联
    @current_user.members.joins(member_identity: { type: klass_names }).first
  end

  # 获取已认证的对象（用于日志等）
  def authenticated_auth
    if respond_to?(:current_auth) && current_auth.present?
      current_auth
    else
      'Unknown'
    end
  end

  # 类方法
  module ClassMethods
    def auth_action(klass_sym, options = {})
      # 提取skip_error选项
      auth_opts = { skip_error: options.delete(:skip_error) }

      # 更新配置
      current_config = auth_config.dup
      current_config[:skip_error] = auth_opts[:skip_error] if auth_opts[:skip_error]
      current_config[:klass_syms] = Array(klass_sym).map(&:to_sym)
      current_config[:klasses] = Array(klass_sym).map do |sym|
        # 将 'rms/expert' 转换为 'rms_expert' 以用于方法/变量名
        method_suffix = sym.to_s.gsub('/', '_')

        # 为每个认证类型定义 current_#{type} 方法，使用处理后的后缀
        define_method("current_#{method_suffix}") do
          current_auth # 确保 current_auth 被调用以设置 @current_auth
          # 如果 @current_auth 的类型匹配当前符号对应的类，则设置实例变量
          if @current_auth&.class == sym.to_s.camelize.safe_constantize
            instance_variable_set("@current_#{method_suffix}", @current_auth)
          else
            instance_variable_set("@current_#{method_suffix}", nil) # 或者保持为 nil
          end
          instance_variable_get("@current_#{method_suffix}") # 返回设置的值
        end
        sym.to_s.camelize.constantize
      end

      self.auth_config = current_config

      # 设置自定义验证器
      if (validator = options.delete(:validator))
        define_method('custom_auth_validation') do
          case validator
          when Proc
            instance_exec(&validator)
          when Symbol, String
            method_name = validator.to_sym
            send(method_name) if respond_to?(method_name, true)
          else
            raise ArgumentError, "Invalid validator type: #{validator.class}"
          end
        end
      end

      # 使用Rails的before_action过滤器，支持:only和:except选项
      prepend_before_action(:auth_perform, options.slice(:only, :except))

      # 定义通用的current_*方法
      unless method_defined?(:current_auth)
        define_method('current_auth') { @current_auth }
        define_method('current_user') { @current_user }
        define_method('current_member') { @current_member }
        define_method('current_app') { @current_app }
        define_method('current_tenant') { @current_tenant }
      end
    end
  end
end
