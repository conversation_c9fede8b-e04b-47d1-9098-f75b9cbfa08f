module ActionController::LimitAction
  extend ActiveSupport::Concern

  included do
    before_action :limit_commit_operation!, only: [:create, :destroy]
  end

  class_methods do
    def disable_operation_limit!
      @disable_operation_limit = true
    end

    def operation_limit_disabled?
      @disable_operation_limit == true
    end
  end

  def limit_commit_operation!
    # 如果全局禁用了限制，直接返回
    return if self.class.operation_limit_disabled?

    # 如果不是生产环境，跳过限制
    return unless Rails.env.production?

    # 如果请求头包含 NO_LIMIT，跳过限制
    return if request.env['HTTP_NO_LIMIT'].present?

    identifier = respond_to?(:current_auth) ?
      current_auth&.id : request.remote_ip

    key = [params[:controller], params[:action], params[:limit_identifier], identifier].compact * '_'
    existing_key = $token_redis.set(key, 1, nx: true, ex: 1)

    raise Error::BaseError.new(message: '您的操作过快', status: 430) unless existing_key
  end
end
