module ActionController::AsyncActionable
  extend ActiveSupport::Concern

  EXCLUDED_HEADERS = %w[
    HTTP_HOST HTTP_VERSION HTTP_CONNECTION HTTP_USER_AGENT HTTP_ACCEPT_ENCODING HTTP_ACCEPT_LANGUAGE
  ]

  included do
  end

  class_methods do
    def async_action(action_name, flag:)
      async_method_name = "#{action_name}_async"

      define_method(async_method_name) do
        headers = request.headers.env.select do |k, _|
          (k.start_with?('HTTP_') || k == 'CONTENT_TYPE' || k == 'Accept') && !EXCLUDED_HEADERS.include?(k)
        end

        async_task = ControllerAsyncTask.create!(
          user: current_user,
          flag: flag,
          controller: self.class.name,
          controller_action: action_name,
          payload: {
            params: request.parameters.merge(async_task: 'async'),
            headers: headers
          }
        )
        async_task.perform_later
        render json: { status: 'queued', task_id: async_task.id }
      end
    end
  end
end
