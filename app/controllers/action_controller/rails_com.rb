module ActionController::RailsCom
  extend ActiveSupport::Concern

  included do
    include ActionController::Rescue
    include ActionController::Tanent
    include ActionController::AuthAction
    include ActionController::ActionAuthorize
    include ActionController::PermitAction
    include ActionController::LimitAction
    include Com::Controller::TaStatistic
    include ActionController::Caching
    include ActionController::PunditPermission
    include ActionController::AsyncActionable

    # rescue_frem ActiveRecord::RecordNotFound do |_e|
    #   render json: { error: '查找对象或路由不存在', message: '查找对象或路由不存在' }, status: 404
    # end

    rescue_from ActiveRecord::RecordInvalid do |e|
      render json: { error: e.message, message: e.message }, status: 422
    end

    rescue_from Pundit::NotAuthorizedError do |e|
      render json: { error: e.message, message: e.message }, status: 403
    end

    rescue_from ActiveModel::Error do |e|
      render json: { error: e.message, message: e.message }, status: 422
    end

    rescue_from Error::BaseError do |e|
      render json: { code: e.code, message: e.message, errors: e.errors }, status: e.status
    end

    prepend_before_action :decode_iv_encrypted_parameters
    prepend_before_action :decode_query_parameters

    def render(*args, &block)
      super

      return unless request.headers['HTTP_IV_ENCRYPT'] || request.headers['HTTP_IV_DECODE'] || ENV['FORCE_IV_ENCRYPT']
      return if request.headers['HTTP_DISABLE_IV_ENCRYPT'] && request.headers['HTTP_IV_DECODE'].blank? # 支持可以强制关闭
      return unless response.headers['Content-Type'].to_s.include?('application/json')

      iv = Com::AesCrypt.random_iv
      iv64 = Com::AesCrypt.b64enc iv
      password = Digest::SHA256.digest(ENV.fetch('IV_KEY', 'tallty.com'))
      iv_encrypted = Com::AesCrypt.encrypt(password, iv, response.body)
      response.body = { iv_encrypted: iv_encrypted, iv64: iv64 }.to_json
      response.headers.merge!('iv-encrypt' => true)
    end

    private

    def decode_iv_encrypted_parameters
      iv_encrypted = params.delete(:iv_encrypted)
      if request.headers['HTTP_IV_DECODE'].present? && iv_encrypted.present?
        iv = Base64.decode64 request.headers['HTTP_IV_DECODE']
        password = Digest::SHA256.digest(ENV.fetch('IV_KEY', 'tallty.com'))
        params.merge!(
          JSON.parse(
            Com::AesCrypt.decrypt(password, iv, iv_encrypted).presence || {}.to_json,
          ),
        )
        Rails.logger.info params
      end
    rescue StandardError
      raise Error::EncryptError
    end

    def decode_query_parameters
      return unless request.headers['HTTP_RANSACK_DECODE']

      request.query_parameters.each do |key, value|
        params[key] = JSON.parse(value) if value.is_a?(String)
      rescue StandardError
        next
      end
    end

    def check_iv_encrypted
      raise Error::EncryptError unless request.headers['HTTP_IV_DECODE'].present?
    end

    def disable_iv_encrypted(*args)
      request.headers['HTTP_DISABLE_IV_ENCRYPT'] = 1
    end
  end

  module ClassMethods
    def encrypt_action(**options)
      before_action :check_iv_encrypted, **options
    end

    def disable_encrypt_action(**options)
      before_action :disable_iv_encrypted, **options
    end
  end
end
