module ActionController::ActionAuthorize
  extend ActiveSupport::Concern

  included do
    before_action :check_action_authorize!
  end

  class_methods do
    def skip_permit(only: [], except: [])
      raise ArgumentError, '不能同时设置 only 和 except 参数' if only.present? && except.present?

      @skip_permit = only.empty? && except.empty?
      @skip_permit_only_actions = only
      @skip_permit_except_actions = except
    end
  end

  private

  def check_action_authorize!
    return unless @current_user
    skip_permit = self.class.instance_variable_get(:@skip_permit)
    if skip_permit
      @permit_handled = true
      return
    end

    if defined? Permit::Model::User
      export_actions = %w(export export_headers)
      import_actions = %w(upload_excel excel import import_headers exchange export_import_template)
      index_actions = %w(list_index group_index)
      alias_key = if export_actions.include?(action_name)
                    "export"
                  elsif import_actions.include?(action_name)
                    "import"
                  elsif index_actions.include?(action_name)
                    "index"
                  else
                    action_name
                  end
      authorize_key = "#{self.class.name}##{alias_key}"

      unless skip_permit.nil?
        @permit_handled = true
        only_actions = self.class.instance_variable_get(:@skip_permit_only_actions) || []
        except_actions = self.class.instance_variable_get(:@skip_permit_except_actions) || []
        return if only_actions.include?(alias_key.to_sym)
        return unless except_actions.include?(alias_key.to_sym)
      end

      if @current_user.send(:is_god?)
        @permit_handled = true
        return
      end
      return unless @current_user.send(:defined_permission?, authorize_key)

      @permit_handled = true
      raise Error::PermitError unless @current_user.send(:can_authorize?, authorize_key)
    end
  end
end