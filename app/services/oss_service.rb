class OssService
  # 上传更新文件
  # file [required, :File] :file # 文件流
  # @options filename [:String] :filename # 文件名称
  # @options key [:String] :key # 对象名称，唯一标识符
  # @options prefix [:String] :prefix # 文件保存的路径前缀
  # @options tmp [:Boolean] :tmp # 临时文件 会定期删除的文件
  # @options tmp_path [:String] :tmp_path # 临时文件的路径, 默认是tmp

  # example
  # OssService.put(File.open(filepath), tmp: true)
  def self.put(file, s3_options: {}, **options)
    # 从文件流获取文件信息
    filename = options[:filename] || File.basename(file.path)
    file = file.respond_to?(:read) ? file.read : file
    oss_key = file.is_a?(String) ? Digest::MD5.hexdigest(file) : SecureRandom.hex
    key = options[:key] || generate_key(**options.merge(filename: filename, key: oss_key))
    content_type = RailsCom::ActiveStorage::FileType.new(filename).mime_type
    s3_options.merge!(content_type: content_type) if content_type.to_s.include?('image')
    $s3_client.bucket.put_object(s3_options.merge(key: key, body: file))

    # 数据持久化
    blob = ActiveStorage::Blob.find_or_initialize_by(key: key)
    blob.update!(
      app_code: options[:prefix] || App.first.code,
      filename: filename,
      content_type: content_type,
      byte_size: file.size,
      service_name: 's3',
      checksum: 'nUNz0V5J2cMqs3AyvO/3UQ=='
    )
    blob.as_json.merge(url: blob.url, fileType: File.extname(filename).gsub(/^\./, ''))
  end

  # 删除oss上的文件
  def self.delete(key)
    $s3_client.bucket.delete_objects(delete: { objects: [{key: key}] })
  end

  # 默认生成key
  def self.generate_key(**options)
    key = options[:key] + File.extname(options[:filename].to_s)
    prefix = options[:prefix] || App.first.code
    tmp = options[:tmp] ? options[:tmp_path] || 'tmp' : Time.now.year
    [prefix, tmp, key].compact.join('/').downcase
  end
end