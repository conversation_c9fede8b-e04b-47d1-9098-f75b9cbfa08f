require 'singleton'
require 'open-uri'
require 'execjs'

class Jsonata
  include Singleton

  def initialize
    source = URI.open('https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/public/jsonata.min.js').read
    @context = ExecJS.compile(source)
  end

  def evaluate(expression, data: {})
    expression = expression.strip if expression.present?
    @context.eval "(function() { var expression = jsonata('#{expression}'); return expression.evaluate(#{JSON.dump(data)}) })()"
  end

  def self.evaluate(expression, data: {})
    instance.evaluate(expression, data: data)
  end
end
