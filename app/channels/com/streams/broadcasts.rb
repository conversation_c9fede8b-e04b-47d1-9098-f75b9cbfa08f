# frozen_string_literal: true

module Com::Streams::Broadcasts

  def broadcast_action_to(*streamables, action:, target: nil, **opts)
    broadcast_stream_to(*streamables, content: {
      action: action,
      target: target
    }.merge(opts))
  end

  def broadcast_action_later_to(*streamables, action:, target: nil, **opts)
    Com::Streams::ActionBroadcastJob.perform_later \
      stream_name_from(streamables), action: action, target: target, **opts
  end

  def broadcast_stream_to(*streamables, content:)
    stream_name_from(streamables).each do |streamable|
      Rails.logger.info("channel: #{streamable}, content: #{content}")

      ActionCable.server.broadcast streamable, content
    end
  end

  private

  # streamables为多个时会为每个数组内的频道进行推送
  def stream_name_from(streamables)
    if streamables.is_a?(Array)
      streamables.flatten.uniq.map { |streamable| stream_name_from(streamable) }
    else
      streamables.then { |streamable| streamable.try(:to_gid_param) || streamable.to_param }
    end
  end
end
