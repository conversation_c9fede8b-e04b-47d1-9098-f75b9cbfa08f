module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    def connect
      self.current_user = find_verified_user
    end

    private

    def find_verified_user
      account_info = begin
                       User.auth!(request.params[:token])
                     rescue Error::TokenError
                       reject_unauthorized_connection
                     end
      app = App.find_by(code: account_info['app_id']) || App.first
      verified_user = app.users.effective.find_by(account: account_info['account'])

      reject_unauthorized_connection if verified_user.blank?

      verified_user
    end
  end
end
