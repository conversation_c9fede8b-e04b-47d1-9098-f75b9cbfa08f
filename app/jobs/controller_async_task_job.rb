class ControllerAsync<PERSON>ask<PERSON>ob < ApplicationJob
  queue_as :default

  def perform(async_task)
    async_task.processing!
    controller_class = async_task.controller.constantize
    controller = controller_class.new

    payload = async_task.payload.symbolize_keys
    request_params = payload[:params].symbolize_keys
    request_headers = payload[:headers]

    request_env = Rack::MockRequest.env_for('/').merge(
      'rack.input' => StringIO.new,
      'action_dispatch.request.request_parameters' => request_params
    ).merge(request_headers)

    request = ActionDispatch::Request.new(request_env)
    controller.request = request
    controller.response = ActionDispatch::TestResponse.new

    begin
      controller.dispatch(async_task.controller_action, controller.request, controller.response)
      if controller.response.successful? # 检查 HTTP 状态码
        if controller.response.headers['Content-Type'].include?('application/json')
          result = JSON.parse(controller.response.body)
          async_task.update(result: result, state: 'completed')
        else
          filename = get_filename(controller.response.headers, async_task.name)
          result = OssService.put(controller.response.body, tmp: true, filename: filename)
          async_task.update(result: result, state: 'completed')
        end
      else
        async_task.update(result: { error: 'Request failed', status: controller.response.status }, state: 'failed')
      end
    rescue StandardError => e
      async_task.update(result: { error: e.message, backtrace: e.backtrace }, state: 'failed')
      raise e
    end
  end

  def get_filename(headers, task_name=nil)
    task_name ||= Time.now.strftime('%Y%m%d%H%M%S')
    if headers.dig('Content-Type').to_s == 'application/zip'
      [task_name, 'zip'] * '.'
    else
      content_disposition = headers['content-disposition'].to_s
      filename_match = /filename\*=UTF-8''(.+)/.match(content_disposition)
      filename_match && filename_match[1] ?
        URI.decode_www_form_component(filename_match[1]) :
        [task_name, 'xlsx'] * '.'
    end
  end
end