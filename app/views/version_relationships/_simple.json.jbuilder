json.partial! 'version_relationships/single', version_relationship: version_relationship

json.app version_relationship.app, partial: 'apps/single', as: :app
json.operator version_relationship.operator&.as_jbuilder_json(partial: 'single')
# json.resource version_relationship.resource&.as_jbuilder_json(partial: 'single')
# json.real_resource version_relationship.resource&.as_jbuilder_json(partial: 'single')
json.version version_relationship.version&.as_jbuilder_json(partial: 'single')
