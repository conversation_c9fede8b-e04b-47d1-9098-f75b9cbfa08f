json.extract!(
  data_form,
  *data_form.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :create_user_id,
  :source_type,
  :source_id,
  :record_type,
  :record_id,
  :type,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :source_flag,
  :state,
  :payload,
  :summary,
  :form_conf,
  # :new_form_conf,
  :options,
  :meta,
  :form_conf_seq,
)

json.record data_form.record.as_jbuilder_json(partial: :single) if data_form.record
