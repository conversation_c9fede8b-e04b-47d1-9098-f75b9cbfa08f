json.partial! 'api_settings/single', api_setting: api_setting
json.extract!(
  api_setting,
  *api_setting.class.try(:extra_view_attributes, 'simple'),
)

json.model_define api_setting.model_define, partial: 'model_defines/single', as: :model_define
json.app api_setting.app, partial: 'apps/single', as: :app

json.ta_statistics api_setting.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
