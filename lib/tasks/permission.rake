namespace :permission do
  EXCLUDE_ACTIONS = %w(list_index group_index export_headers upload_excel excel import_headers exchange export_import_template)
  DEFAULT_ACTIONS = { index: 10, show: 20, create: 30, update: 40, destroy: 50, import: 60, export: 70 }

  desc "migrate permission of default role"
  task export: :environment do
    puts "path: #{Dir.pwd}"
    engine_name = Dir.pwd.split('/').last
    is_api = engine_name.to_s.end_with?('api')
    engine_klass = "#{engine_name.to_s.camelize}::Engine".safe_constantize
    abort "Engine can not initialized. Current path: #{Dir.pwd}" if engine_klass.nil? && !is_api

    require_relative "#{Dir.pwd}/config/routes"

    routes = Rails.application.routes.routes
    mod = engine_name&.split('_')&.last
    route_map = {} # put/patch 重复
    roles_info = {}
    all_roles = []
    routes.each do |r|
      controller_path = r.defaults[:controller]
      controller = controller_path&.camelize
      action = r.defaults[:action]
      next if controller.blank? || controller.start_with?("Rails::")
      next if EXCLUDE_ACTIONS.include?(action)
      next if route_map["#{controller}#{action}"]

      klass = "#{controller}Controller".safe_constantize
      next unless klass

      permit_roles = klass.instance_variable_get("@permit_roles") || []
      next if permit_roles.empty?
      all_roles = all_roles.concat(permit_roles).uniq

      need_skip = klass.instance_variable_get("@skip_permit")
      unless need_skip.nil?
        next if need_skip
        skip_only_actins = klass.instance_variable_get("@skip_permit_only_actions")
        skip_except_actins = klass.instance_variable_get("@skip_permit_except_actions")
        next if skip_only_actins.include?(action.to_sym)
        next if skip_except_actins.present? && !skip_except_actins.include?(action.to_sym)
      end

      permit_roles.each do |role|
        permissions = roles_info.fetch(role.to_s, [])
        permissions << { mod: mod, key: "#{klass}##{action}", action: action, klass: klass.to_s,
                         position: DEFAULT_ACTIONS[action.to_sym] || 100 }
        roles_info[role.to_s] = permissions
      end
      route_map["#{controller}#{action}"] = true
    end

    puts "roles: #{all_roles}"

    all_roles.each do |role|
      role_name = role.to_s
      directory_path = File.expand_path("config/permissions", Dir.pwd)
      FileUtils.mkdir_p(directory_path)
      file_path = File.join(directory_path, "#{role_name}.json")
      data = { mod: mod, role: role_name, actions: roles_info[role_name] || [] }
      File.open(file_path, "w") { |f| f.puts(data.to_json) }
    end
    puts "done."
  end
end