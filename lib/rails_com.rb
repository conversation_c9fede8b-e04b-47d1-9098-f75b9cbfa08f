# frozen_string_literal: true

require 'rails_com/version'
require 'rails_com/engine'

require 'responders'
require 'jbuilder'
require 'rack/cors'
require 'strip_attributes'
require 'will_paginate'
require 'will_paginate/array'
require 'acts_as_list'
require 'ancestry'
require 'rolify'
require 'redis'
require 'redis/objects'
require 'redis/namespace'
require 'by_star'
require 'groupdate'
require 'paranoia'
require 'paper_trail'
require 'ohm'
require 'typhoeus'
require 'uuidtools'
require 'attr_json'
require 'ransack'
require 'ransack-enum'
require 'pundit'
require 'deep_cloneable'
require 'deep_pluck'
require 'active_record/events'
require 'default_value_for'
require 'zip_tricks'
require 'mime/types'
require 'nilify_blanks'

require 'rails_com/responder'
require 'rails_com/uppy_s3_multipart'
require 'rails_com/active_record_extended'
require 'rails_com/aasm_state_event'
require 'rails_com/core'
require 'rails_com/ransack'
require 'rails_com/active_record'
require 'rails_com/active_model'
require 'rails_com/action_controller'
require 'rails_com/action_text'
require 'rails_com/action_mailbox'
require 'rails_com/active_storage'
require 'rails_com/action_route'
require 'rails_com/string'
# require 'rails_com/i18n_extension'
# Rails extension
require 'rails_com/generators'
require 'rails_com/action_route/base_inject_route'

Rolify.remove_role_if_empty = false
Groupdate.week_start = :monday

module RailsCom
  # Your code goes here...
end
