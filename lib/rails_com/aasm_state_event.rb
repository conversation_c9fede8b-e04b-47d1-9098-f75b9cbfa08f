require 'aasm'

module RailsCom::AasmStateEvent
  extend ActiveSupport::Concern

  included do
    attr_accessor :aasm_state_event

    def aasm_state_event= event
      return if event.blank?

      event = event.to_s.strip

      if aasm.events.map.include? event.to_sym
        @aasm_state_event = event
        send :"#{event}"
      else
        raise Error::AasmEventError.new
      end
    end

    def trigger_aasm_state_event token:, event:, **options
      self.update! aasm_state_event: event
    end
  end

  class_methods do
  end
end

AASM.include(RailsCom::AasmStateEvent)
