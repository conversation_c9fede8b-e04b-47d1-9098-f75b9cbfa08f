# frozen_string_literal: true

module RailsCom::ActiveStorage::Attachment
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :record, polymorphic: true, optional: true
    belongs_to :blob,  optional: true

    attribute :name, :string, null: false

    index [ :record_type, :record_id, :name, :blob_id ], unique: true
  end

  class_methods do
  end
end

ActiveSupport.on_load(:active_storage_attachment) do
  include RailsCom::ActiveStorage::Attachment
end
