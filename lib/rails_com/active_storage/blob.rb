# frozen_string_literal: true

module RailsCom::ActiveStorage::Blob
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    attribute :app_code, :string, comment: 'app标识'
    attribute :key, :string, comment: 'key'
    attribute :filename, :string, comment: '文件名称'
    attribute :content_type, :string, comment: '文件类型'
    attribute :metadata, :jsonb, comment: '额外信息'
    attribute :service_name, :string, comment: '服务名称'
    attribute :byte_size, :integer, comment: '文件大小'
    attribute :checksum, :string, comment: '校验位'

    index [:key], unique: true
  end

  class_methods do
    # 迁移到soa_file
    def async_active_storage!(prefix: nil, upload: false, dir: '')
      if defined?(SoaFile::AFile)
        SoaFile::AFile.where(source_type: [nil, 'fail']).find_each do |f|
          key = f.full_path.to_s.gsub('public/', '')
          key = [prefix, key] * '/' if prefix
          # 上传文件到minio
          file_path = File.join(dir, f.full_path)
          next unless File.exist?(file_path)
          $s3_client.bucket.put_object(key: key, body: File.open(file_path), content_type: f.mime_type) if upload
          b = ActiveStorage::Blob.find_or_initialize_by(key: key)
          b.update(
            service_name: 's3',
            content_type: f.mime_type,
            filename: f.file_name,
            byte_size: f.file_size,
            checksum: 'nUNz0V5J2cMqs3AyvO/3UQ=='
          )
        rescue Exception => e
          Rails.logger.info "=====#{e.message}====="
        end
      end
    end

    def generate_blob_record!(url: nil, upload: true, app_code: nil)
      file = URI.open(url)
      key = SecureRandom.hex
      key = [app_code, Time.now.year, key].compact.join('/').downcase
      blob = ActiveStorage::Blob.create(
              app_code: app_code,
              filename: key,
              key: key,
              content_type: file.content_type,
              byte_size: file.size,
              service_name: 's3',
              checksum: 'nUNz0V5J2cMqs3AyvO/3UQ=='
            )
      $s3_client.bucket.put_object(key: key, body: file) if upload
      file = nil
      blob.as_json.merge(url: blob.url)
    end
  end
end

ActiveSupport.on_load(:active_storage_blob) do
  include RailsCom::ActiveStorage::Blob
end
