class RailsCom::ActiveStorage::FileType
  attr_accessor :file_name

  def initialize(file_name)
    @file_name = file_name
  end

  def self.check_mime_type!(file_name, types: %w[document video audio compressed image], content_type: nil)
    return if ENV['SKIP_CHECK_MIME_TYPE']

    file_type = new(file_name)
    unless content_type == 'audio/x-m4a'
      raise Error::FileTypeError if content_type.present? && content_type != file_type.mime_type
    end
    # raise Error::FileTypeError unless types.include?(file_type.category_type)
  end

  def mime_type
    MIME::Types.type_for(file_name).first.to_s.gsub('application/mp4', 'video/mp4')
  end

  def file_type
    mime_type.split('/').last
  end

  def media_type
    mime_type.split('/').first || 'other'
  end

  def category_type
    if %r{(ms-excel|ms-powerpoint|msword|vnd\.visio|officedocument|pdf|text/)} =~ mime_type
      'document'
    elsif /zip|rar|compressed/ =~ mime_type
      'compressed'
    else
      media_type
    end
  end

  def extname
    File.extname(file_name).gsub(/^\./, '')
  end
end
