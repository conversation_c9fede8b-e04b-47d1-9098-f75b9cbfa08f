# frozen_string_literal: true

module RailsCom::ActiveStorage::VariantRecord
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :blob, optional: true

    attribute :variation_digest, :string, null: false

    index %i[ blob_id variation_digest ], unique: true
  end

  class_methods do
  end
end

ActiveSupport.on_load(:active_storage_variant_record) do
  include RailsCom::ActiveStorage::VariantRecord
end
