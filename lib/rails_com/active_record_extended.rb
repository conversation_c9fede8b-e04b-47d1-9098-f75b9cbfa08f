require 'active_record_extended'

module ArelPostgreSQL
  def visit_Arel_Nodes_Contains(object, collector)
    # left_column = object.left.relation.name.classify.constantize.columns.detect do |col|
    left_column = object.left.relation.instance_variable_get('@klass').columns.detect do |col|
      matchable_column?(col, object)
    end

    if [:hstore, :jsonb].include?(left_column&.type)
      visit_Arel_Nodes_ContainsHStore(object, collector)
    elsif left_column.try(:array)
      visit_Arel_Nodes_ContainsArray(object, collector)
    else
      visit_Arel_Nodes_Inet_Contains(object, collector)
    end
  end
end

Arel::Visitors::PostgreSQL.prepend(ArelPostgreSQL)
