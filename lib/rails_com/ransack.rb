Ransack.configure do |config|
  config.add_predicate 'jcont', arel_predicate: 'contains', formatter: proc { |v| JSON.parse(v) }
end

module Ransack
  module Nodes
    module Bindable
      def ransacker
        if attr_name.include?('.')
          @ransacker_args = attr_name
          klass._ransackers['jsonb']
        else
          klass._ransackers[attr_name]
        end
      end
    end

    class Sort
      alias valid_origin? valid?

      def valid?
        bound? && attr &&
          context.klassify(parent).ransortable_attributes(context.auth_object)
                 .include?(attr_name.split('.').first)
      end
    end

    class Attribute
      alias valid_origin? valid?

      def valid?
        bound? && attr &&
          (
            context.klassify(parent).ransackable_attributes(context.auth_object).include?(attr_name.split('.').last) ||
            context.klassify(parent).ransackable_attributes(context.auth_object).include?(attr_name.split('.').first)
          )
      end
    end
  end

  module Adapters
    module ActiveRecord
      class Context
        alias type_for_origin type_for

        def type_for(attr)
          return nil unless attr && attr.valid?

          name         = attr.arel_attribute.name.to_s.split('.').first
          # Arel::Nodes::TableAlias -> table_name
          # Arel::Table -> name
          relation     = attr.arel_attribute.relation
          table        = relation.respond_to?(:table_name) ? relation.table_name : relation.name
          schema_cache = klass.connection.schema_cache
          raise "No table named #{table} exists." unless schema_cache.send(:data_source_exists?, table)

          attr.klass.columns.find { |column| column.name == name }.type
        end

        def ransackable_attribute?(str, klass)
          str = str =~ /_or_|_and_/ ?
            str : str.split('.').first
          klass.ransackable_attributes(auth_object).include?(str) ||
            klass.ransortable_attributes(auth_object).include?(str)
        end
      end
    end
  end
end
