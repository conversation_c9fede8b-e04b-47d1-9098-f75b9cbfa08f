require "uppy/s3_multipart"
require 'aws-sdk-s3'

if ENV["S3_BUCKET"].present?
  bucket = Aws::S3::Bucket.new(
    name:              ENV["S3_BUCKET"],
    access_key_id:     <PERSON>NV["S3_ACCESS_KEY_ID"], # "AccessKey" value
    secret_access_key: ENV["S3_SECRET_ACCESS_KEY"], # "SecretKey" value
    endpoint:          ENV["S3_ENDPOINT"],   # "Endpoint"  value
    region:            ENV["S3_REGION"],
    force_path_style:  ENV["S3_FORCE_PATH_STYLE"].present?,
    ssl_verify_peer:   ENV["S3_SSL_VERIFY_PEER"].blank?,
  )
  $s3_client = Uppy::S3Multipart::Client.new(bucket: bucket)
end
