module RailsCom
  module ActionRoute
    module BaseInjectRoute
      extend ActiveSupport::Concern

      private

      def inject_controller_methods!(controller_modules)
        begin
          controller_class = controller_class_for(@scope)
          return unless controller_class

          controller_modules.each do |mod, should_include|
            controller_class.include(mod) if should_include.call
          end
        rescue NameError => e
          Rails.logger.debug "#{self.class.name}: Controller not loaded yet: #{e.message}"
        end
      end

      def controller_class_for(scope)
        scope_level = scope[:scope_level_resource]
        return unless scope_level

        module_path = collect_unique_modules(scope)
        controller_name = scope_level.controller.camelize

        if module_path.blank?
          "#{controller_name}Controller".constantize
        else
          "#{module_path}::#{controller_name}Controller".constantize
        end
      end

      def collect_unique_modules(scope)
        return nil unless scope

        modules = []
        current_scope = scope

        while current_scope
          if mod = current_scope[:module]
            mod.split('/').reverse_each do |part|
              modules.unshift(part.camelize) unless modules.include?(part.camelize)
            end
          end
          current_scope = current_scope.parent
        end

        modules.join('::')
      end

      # 处理 only/except 选项的辅助方法
     def filter_actions(actions, options, prefix = nil)
       return actions if options.blank?

        if options[:only].present?
          Array(options[:only]).map { |action| normalize_action_name(action) } & actions
        elsif options[:except].present?
          actions - Array(options[:except]).map { |action| normalize_action_name(action) }
        else
          actions
        end
      end

      def remove_prefix(action, prefix)
        action_str = action.to_s
        if action_str.start_with?("#{prefix}_")
          action_str.sub("#{prefix}_", '').to_sym
        else
          action
        end
      end

      def normalize_action_name(action, prefix = nil)
        action_str = action.to_s
        prefix_str = prefix ? "#{prefix}_" : ""

        if action_str.start_with?(prefix_str)
          action_str[prefix_str.length..-1].to_sym
        else
          action.to_sym
        end
      end

      # Helper method to define REST-like routes
      def define_member_routes(routes_config, prefix: nil, **options)
        @current_prefix = prefix

        filtered_actions = if routes_config.first.is_a?(Hash) && routes_config.first[:action]
                             all_actions = routes_config.map { |config| config[:action] }
                             filter_actions(all_actions, options, prefix)
                           else
                             []
                           end

        routes_config.each do |route_config|
          next if filtered_actions.present? && !filtered_actions.include?(route_config[:action])
          next unless include_action?(route_config[:action])

          http_methods = Array(route_config[:method])
          path = build_path(route_config[:path], prefix)
          action_name = route_config[:controller_action] || build_action_name(route_config[:action], prefix)

          http_methods.each do |method|
            route_options = { on: :member, action: action_name }
            route_options.merge!(route_config[:options]) if route_config[:options]
            send(method, path, route_options)
          end
        end

        @current_prefix = nil
      end

      def build_path(path, prefix)
        return path unless prefix
        path.is_a?(Symbol) ? :"#{prefix}_#{path}" : "#{prefix}_#{path}"
      end

      def build_action_name(action, prefix)
        prefix ? "#{prefix}_#{action}" : action.to_s
      end

      def setup_included_actions(actions, default_actions, options = {})
        if actions.empty? && options.blank?
          @included_actions = default_actions
          return
        end

        if actions.last.is_a?(Hash)
          options = actions.pop
        end

        if actions.include?(:all) || actions.empty?
          @included_actions = filter_actions(default_actions, options)
        else
          @included_actions = filter_actions(actions, options)
        end
      end

      protected

      def include_action?(action)
        @included_actions.include?(action)
      end

      def has_any_actions?(action_group)
        action_group.any? { |action| include_action?(action) }
      end
    end
  end
end
