module RailsCom::ActiveModel::Schedule
  extend ActiveSupport::Concern

  included do
    attribute :start_at, :datetime, comment: '任务开始时间'
    attribute :end_at, :datetime, comment: '任务结束时间'
    attribute :schedule_type, :string, comment: '周期任务'
    attribute :schedule_uid, :string, comment: '随机串'
    enum schedule_type: {
      day: 'day',
      week: 'week',
      month: 'month',
      quarter: 'quarter',
      half_year: 'half_year',
      year: 'year',
      any: 'any',
      fixed: 'fixed', # 固定任务
      none: 'none', # 临时任务
    }, _default: 'none', _suffix: true
    # 记录上一个开始的节点
    belongs_to :schedule_next, class_name: name, optional: true
    has_one :schedule_prev, class_name: name, foreign_key: :schedule_next_id, dependent: :nullify

    scope :scheduleable, -> {
      where(schedule_type: %w[day week month quarter half_year year])
    }

    scope :schedule_next_activate, -> {
      scheduleable.where(schedule_next_id: nil)
    }

    default_value_for(:schedule_uid) { SecureRandom.hex }

    def schedule_related
      where(schedule_uid: schedule_uid)
    end

    def schedule_root
      schedule_prev.present? ?
        schedule_prev.schedule_root : self
    end

    # 在下个任务开始的N个周期内，例如月重复的，则提前N个月
    # 这里会遍历生成下N个任务，跳过中间已经过期的情况
    # 例如当前时间是9月，任务是5月，月任务，则生成的时候，会跳过6-8月的任务，直接生成9月
    # 这里判断所有schedule_prev为空的
    #
    # 这里如果当前任务是还没开始，则不会生成下一个任务
    def schedule_next_activate?
      is_schedule_next =
        schedule_next.blank? &&
        %w[day week month half_year quarter year].include?(schedule_type) &&
        (start_at.present? || end_at.present?)

      schedule_start_at_enable = start_at.present? ? start_at + schedule_delta <= Time.now : false
      schedule_end_at_enable = end_at.present? ? end_at <= Time.now : false

      is_schedule_next && (schedule_start_at_enable || schedule_end_at_enable)
    end

    def schedule_delta
      case schedule_type.to_s
      when 'quarter'
        3.month
      when 'half_year'
        6.month
      else
        1.send(schedule_type)
      end
    end

    # 根据schedule_delta 和当前的 start_at | end_at，计算出下一个周期和现在的schedule_dynamic_delta
    def schedule_dynamic_delta(schedule_time_at, history: false)
      return nil unless schedule_time_at.present?

      dynamic_time_at = schedule_time_at

      if history
        dynamic_time_at += schedule_delta
      else
        dynamic_time_at += schedule_delta while dynamic_time_at < Time.now
      end

      dynamic_time_at
    end

    # 根据schedule_delta 和当前的 start_at | end_at，计算出下一个周期和现在的schedule_dynamic_delta
    def next_start_at(history: false)
      schedule_dynamic_delta(start_at, history: history)
    end

    def next_end_at(history: false)
      if start_at.present? && end_at.present?
        next_start_at(history: history) + (end_at - start_at)
      else
        schedule_dynamic_delta(end_at, history: history)
      end
    end

    # 触发生成下一个任务，并且判断这个任务是否是activate，如果是则递归创建
    # 执行最多需要生成两次任务，在框架里支持，用户只需要实现已知start_at和end_at的方法就可以
    def schedule_next_activation(history: false)
      return unless schedule_next_activate?

      _next_start_at = start_at.present? ? next_start_at(history: history) : nil
      _next_end_at = end_at.present? ? next_end_at(history: history) : nil

      _next_record = generate_schedule_next(start_at: _next_start_at, end_at: _next_end_at)
      _next_record.update! schedule_uid: schedule_uid
      update! schedule_next: _next_record

      # 递归判断下一个任务是否可以继续执行
      _next_record.schedule_next_activation if _next_record.schedule_next_activate?
    end

    def generate_schedule_next(start_at:, end_at:)
      p "#{self.class.name} generate_schedule_next not implement"
    end
  end

  class_methods do
  end
end
