require 'active_model/type'
module RailsCom::ActiveModel
  module TypeValue
    attr_reader :options, :comment

    def initialize(precision: nil, limit: nil, scale: nil, comment: nil, **options)
      @options = options
      @comment = comment
      super(precision: precision, limit: limit, scale: scale)
    end

    module ClassMethods
      def cast_type_value(object)
        if object.is_a? ActiveModel::Type::Value
          object
        elsif object.respond_to?(:call)
          object.call(ActiveRecord::Type.default_value)
        elsif object.respond_to?(:type)
          ActiveRecord::Type.lookup(object.type)
        else
          ActiveRecord::Type.lookup(object)
        end
      end
    end

    def self.prepended(base)
      base.extend ClassMethods
    end
  end
end

# 将新的方法添加到 ActiveModel::Type::Value 中
ActiveModel::Type::Value.prepend RailsCom::ActiveModel::TypeValue
