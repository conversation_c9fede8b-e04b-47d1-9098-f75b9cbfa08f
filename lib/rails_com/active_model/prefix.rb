module RailsCom
  module ActiveModel
    module Prefix
      extend ActiveSupport::Concern

      class_methods do
        def with_prefix(prefix, &block)
          @current_prefix = prefix.to_s
          class_eval(&block)
          @current_prefix = nil
        end

        def belongs_to(name, scope = nil, **options, &block)
          if @current_prefix
            prefixed_name = add_prefix(name)
            super(prefixed_name, scope, **options, &block)
          else
            super
          end
        end

        def attribute(name, type = nil, **options)
          if @current_prefix
            prefixed_name = add_prefix(name)
            super(prefixed_name, type, **options)
          else
            super
          end
        end

        def has_many(name, scope = nil, **options, &block)
          if @current_prefix
            prefixed_name = add_prefix(name)
            super(prefixed_name, scope, **options, &block)
          else
            super
          end
        end

        def has_one(name, scope = nil, **options, &block)
          if @current_prefix
            prefixed_name = add_prefix(name)
            super(prefixed_name, scope, **options, &block)
          else
            super
          end
        end

        def has_and_belongs_to_many(name, scope = nil, **options, &block)
          if @current_prefix
            prefixed_name = add_prefix(name)
            super(prefixed_name, scope, **options, &block)
          else
            super
          end
        end

        def validates(name, *args, **options, &block)
          if @current_prefix && name.is_a?(Symbol)
            prefixed_name = add_prefix(name)
            super(prefixed_name, *args, **options, &block)
          else
            super
          end
        end

        def scope(name, body = nil, **options)
          if @current_prefix && name.is_a?(Symbol)
            prefixed_name = add_prefix(name)
            super(prefixed_name, body, **options)
          else
            super
          end
        end

        private

        def add_prefix(name)
          return name unless @current_prefix
          return name if name.to_s.start_with?("#{@current_prefix}_")
          "#{@current_prefix}_#{name}".to_sym
        end
      end
    end
  end
end
