String.class_eval do
  # 默认是中国身份证号
  def simple_encode_idcard
    str = clone
    str[6..13] = '********'
    str
  end

  # 默认是中国大陆手机号码11位
  def simple_encode_mobile
    str = clone
    str[3..6] = '****'
    str
  end

  def simple_encode(from, to)
    str = clone
    str[from..to] = '****'
    str
  end

  def common_encode
    w = self.length
    str = clone
    if w == 18
      str = simple_encode_idcard
    elsif w == 11
      str = simple_encode_mobile
    elsif w == 1 || w == 0
      '*'
    elsif w < 6
      str[1..w-2] = '*' * (w - 2)
    else
      str[2..w-3] = '*' * (w - 4)
    end
    str
  end

  def protect_privacy
    if self =~ /\A\d{11}\z/  # 检查是否为手机号，11位数字
      # 隐藏手机号中间四位数字
      self.gsub(/(\d{3})(\d{4})(\d{4})/, '\1****\3')
    elsif self =~ /\A\d{3,}\z/  # 检查是否为超过3位数字的号码
      # 隐藏号码中间一半数字
      middle_length = self.length / 2
      middle_start = (self.length - middle_length) / 2
      self.gsub(/(\d{#{middle_start}})\d{#{middle_length}}(\d{#{self.length - middle_start - middle_length}})/, '\1' + '*' * middle_length + '\2')
    elsif self =~ /\A[\w\s\+\-\*\/\\]{3,}\z/  # 检查是否为包含特殊字符的号码
      # 隐藏号码中间一半字符
      middle_length = self.length / 2
      middle_start = (self.length - middle_length) / 2
      self.gsub(/([\w\s\+\-\*\/\\]{#{middle_start}})[\w\s\+\-\*\/\\]{#{middle_length}}([\w\s\+\-\*\/\\]{#{self.length - middle_start - middle_length}})/, '\1' + '*' * middle_length + '\2')
    else
      # 隐藏姓名中间的字符
      name_length = self.length
      if name_length <= 2
        self
      else
        first_char = self[0]
        last_char = self[-1]
        middle_chars = '*' * (name_length - 2)
        "#{first_char}#{middle_chars}#{last_char}"
      end
    end
  end
end
