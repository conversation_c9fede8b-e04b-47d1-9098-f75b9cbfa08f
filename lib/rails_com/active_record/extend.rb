module RailsCom::ActiveRecord::Extend
  def ta_statistic(stat_condition)
    # 只使用只读的方式
    ActiveRecord::Base.while_preventing_writes do
      if stat_condition.is_a?(Hash)
        Com::Attr::Stat::Collection.new(stat_condition).calc(self)
      else
        stat_condition.calc(self)
      end
    end
  end

  # 通过属性过滤后创建对象，防止出现属性不存在导致的问题
  def ta_create(attributes: {}, extra: {})
    record_attributes =
      if respond_to?(:resource_permitted_params)
        attribute_params = attributes.is_a?(ActionController::Parameters) ? attributes : ActionController::Parameters.new(attributes)
        attribute_params.permit(resource_permitted_params).merge(extra).delete_if { |k, _v| !attribute_method?("#{k}=") }
      else
        attribute_params = attributes.respond_to?(:with_indifferent_access) ? attributes.with_indifferent_access : attributes
        attribute_params.except(:id, :created_at, :updated_at).merge(extra).delete_if { |k, _v| !attribute_method?("#{k}=") }
      end

    create!(record_attributes)
  end

  def human_name
    model_name.human
  end

  def has_taxons(*columns)
    columns.each do |column|
      attribute "#{column}_ancestors", :json
      class_eval <<-RUBY_EVAL, __FILE__, __LINE__ + 1
        before_validation :sync_#{column}_id, if: -> { #{column}_ancestors_changed? }

        def sync_#{column}_id
          _outer_id = Hash(#{column}_ancestors).values.compact.last
          if _outer_id
            self.#{column}_id = _outer_id
          else
            self.#{column}_id = nil
          end
        end
      RUBY_EVAL
    end
  end

  def to_factory_bot
    require 'rails/generators'
    require 'generators/factory_bot/model/model_generator'

    args = [
      name.underscore,
    ]
    cols = columns.reject(&->(i) { %w[id created_at updated_at].include?(i.name) }).map { |col| "#{col.name}:#{col.type}" }

    generator = FactoryBot::Generators::ModelGenerator.new(args + cols, destination_root: Rails.root)
    generator.invoke_all
  end

  def column_attributes
    columns.map do |column|
      r = {
        name: column.name.to_sym,
        type: column.type,
      }
      r.merge! null: column.null unless column.null
      r.merge! default: column.default unless column.default.nil?
      r.merge! comment: column.comment if column.comment.present?
      r.merge! column.sql_type_metadata.instance_values.slice('limit', 'precision', 'scale').compact
      r.symbolize_keys!
    end
  end

  def new_attributes
    news = if table_exists?
             attributes_to_define_after_schema_loads.except(*columns_hash.keys)
           else
             attributes_to_define_after_schema_loads
           end
    news.map do |name, column|
      r = { name: name.to_sym }
      column_type = ActiveModel::Type::Value.cast_type_value(column[0])
      _type = column_type.respond_to?(:name) ?
        column_type.name : column_type.type
      r.merge! type: _type

      r.merge! comment: column_type.comment if column_type.comment.present?

      r.symbolize_keys!
    end
  end

  def custom_attributes
    defined_keys = attributes_to_define_after_schema_loads.keys

    ref_ids = reflections.values.select(&:belongs_to?)
    ref_ids.map! { |reflection| [reflection.foreign_key, reflection.foreign_type] }
    ref_ids.flatten!
    ref_ids.compact!
    defined_keys += ref_ids

    defined_keys += all_timestamp_attributes_in_model
    defined_keys.prepend primary_key
    defined_keys.map(&:to_s)

    columns_hash.except(*defined_keys).map do |name, column|
      r = {
        name: name.to_sym,
        type: column.type,
      }
      r.merge! null: column.null unless column.null
      r.merge! default: column.default unless column.default.nil?
      r.merge! comment: column.comment if column.comment.present?
      r.merge! column.sql_type_metadata.instance_values.slice('limit', 'precision', 'scale').compact
      r.symbolize_keys!
    end
  end

  def nested_attribute_names
    nested_attributes_options.keys.map { |key| "#{key}_attributes" }
  end

  ###  model_conf  ###  start  ###
  def batch_generate_model_conf!(register_inheritance_class: true)
    ActiveRecord::Base.descendants.each do |klass|
      next unless connection.table_exists?(klass.table_name)
      next unless register_inheritance_class || klass.superclass.to_s == 'ApplicationRecord'
      generate_model_conf!(klass: klass)
    end
  end

  def generate_model_conf!(klass: nil)
    klass ||= self
    reflections = klass.reflect_on_all_associations.map do |reflection|
                    {
                      key: reflection.name.to_s,
                      name: reflection.name.to_s,
                      mode: reflection.macro.to_s == 'has_many' ? 'many' : 'one',
                      klass: reflection.class_name
                    }
                  end

    attrs = klass.columns.map do |column|
              {
                key: column.name,
                name: column.comment || column.name,
                cast_type: column.type.to_s
              }
            end

    conf = { reflections: reflections, attrs: attrs }
    model_conf = ModelConf.find_or_initialize_by(klass: klass.name.downcase)
    model_conf.update!(name: klass.name.to_s, conf: conf)
  end
  ###  model_conf  ###  =end=   ###
end

ActiveSupport.on_load :active_record do
  extend RailsCom::ActiveRecord::Extend
end
