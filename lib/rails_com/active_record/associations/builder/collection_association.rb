# frozen_string_literal: true

module ActiveRecord::Associations::Builder # :nodoc:
  class CollectionAssociation  #:nodoc:
    def self.define_writers(mixin, name)
      super

      mixin.module_parent.class_eval <<-CODE, __FILE__, __LINE__ + 1
        after_create :save_#{name}_association_ids
        after_create :save_#{name}_association
      CODE

      mixin.class_eval <<-CODE, __FILE__, __LINE__ + 1
        def #{name.to_s.singularize}_ids=(ids)
          if new_record?
            @_#{name}_association_ids = ids
          else
            association(:#{name}).ids_writer(ids)
          end
        end

        def #{name}=(value)
          if new_record?
            @_#{name}_association = value
          else
            association(:#{name}).writer(value)
          end
        end

        private

        def save_#{name}_association_ids
          if @_#{name}_association_ids
            association(:#{name}).ids_writer(@_#{name}_association_ids)
            self.reload
          end
        end

        def save_#{name}_association
          if @_#{name}_association
            association(:#{name}).writer(@_#{name}_association)
            self.reload
          end
        end
      CODE
    end
  end
end
