# frozen_string_literal: true

module RailsCom::ActiveRecord::Broadcastable
  extend ActiveSupport::Concern

  module ClassMethods

    # Configures the model to broadcast creates, updates, and destroys to a stream name derived at runtime by the stream symbol invocation.
    #
    #   class Message < ApplicationRecord
    #     belongs_to :board
    #     broadcasts_to :board
    #   end
    #
    #   class Message < ApplicationRecord
    #     belongs_to :board
    #     broadcasts :id, target: "board_messages"
    #   end
    #
    #   class Message < ApplicationRecord
    #     belongs_to :board
    #     broadcasts ->(message) { [ message.board, :messages ] }, target: ->(message) { { id: message.id, content: message.conent} }
    #   end
    #
    def broadcasts(stream = broadcast_streamables_default, target: broadcast_target_default, **opts)
      after_create_commit -> { broadcast_action_later_to(stream.try(:call, self) || send(stream), action: :create, target: target.try(:call, self) || target, **opts) }
      after_update_commit -> { broadcast_action_later_to(stream.try(:call, self) || send(stream), action: :update, target: target.try(:call, self) || target, **opts) }
      after_destroy_commit -> { broadcast_action_later_to(stream.try(:call, self) || send(stream), action: :destroy, target: target.try(:call, self) || target) }
    end

    # Same as <tt>#broadcasts</tt>, but the designated stream for updates and destroys is automatically set to
    # synchronization transmission
    def sync_broadcasts(stream = broadcast_streamables_default, target: broadcast_target_default, **opts)
      after_create_commit -> { broadcast_action_to(stream.try(:call, self) || send(stream), action: :create, target: target.try(:call, self) || target, **opts) }
      after_update_commit -> { broadcast_action_to(stream.try(:call, self) || send(stream), action: :update, target: target.try(:call, self) || target, **opts) }
      after_destroy_commit -> { broadcast_action_to(stream.try(:call, self) || send(stream), action: :destroy, target: target.try(:call, self) || target) }
    end

    # All default targets will use the return of this method. Overwrite if you want something else than <tt>model_name.plural</tt>.
    def broadcast_target_default
      proc do |obj|
        redis_key = signed_stream_verifier(obj)

        # 若存在则有效时间重置为1分钟, 若不存在则初始化值.
        if $broadcast_redis.exists?(redis_key)
          $broadcast_redis.expire(redis_key, 1.minutes)
        else
          $broadcast_redis.set(redis_key, obj.as_jbuilder_json(partial: 'simple').to_json, ex: 1.minutes)
        end

        {
          id: obj.to_param,
          key: redis_key
        }
      end
    end

    # 生成唯一key, 以updated_at为标记, 确保同一时间的key为一个.
    def signed_stream_verifier(obj)
      signed_stream_verifier_key = obj.respond_to?(:updated_at) ? obj.updated_at.strftime('%Y-%m-%d %H:%M:%S') : Time.now.strftime('%Y-%m-%d %H:%M:%S')
      signed_stream_verifier = ActiveSupport::MessageVerifier.new(signed_stream_verifier_key, digest: 'SHA256', serializer: JSON)
      signed_stream_verifier.generate(obj)
    end

    # 定义默认发送通道的方法
    def broadcast_streamables_default
      proc do |obj|
        [model_name.plural, "#{model_name.plural}:#{obj.to_param}"]
      end
    end
  end

  # Broadcast a named <tt>action</tt>, allowing for dynamic dispatch, instead of using the concrete action methods. Examples:
  def broadcast_action_to(*streamables, action:, target: broadcast_target_default, **opts)
    Com::StreamsChannel.broadcast_action_to(*streamables, action: action, target: target)
  end

  # Same as broadcast_action_to but run asynchronously via a Com::Streams::BroadcastJob.
  def broadcast_action_later_to(*streamables, action:, target: broadcast_target_default, **opts)
    Com::StreamsChannel.broadcast_action_later_to(*streamables, action: action, target: target)
  end

  private

  def broadcast_target_default
    self.class.broadcast_target_default.try(:call, self)
  end

end

ActiveSupport.on_load :active_record do
  include RailsCom::ActiveRecord::Broadcastable
end
