module RailsCom::ActiveRecord::Include
  extend ActiveSupport::Concern

  included do
    # include Com::Ext::Broadcastable

    def initialize(*attrs, &block)
      super(*attrs, &block)
      self.class.after_initialize_callbacks&.each(&:call)
    end

    class_attribute :after_initialize_callbacks
    class_attribute :indexes_to_define_after_schema_loads, instance_accessor: false, default: []
    class_attribute :ransackable_scope_defines, instance_accessor: true, default: []
    # 可以用来存储对于对象统计的缓存处理
    attr_accessor :ta_statistics

    scope :ci_where, ->(attribute, value) { where("lower(#{attribute}) = ?", value.to_s&.downcase) }
    scope :ci_find!, ->(attribute, value) { ci_where(attribute, value).take! }
    scope :random_order, -> { order('RANDOM()') }

    ransacker :id_string do |parent|
      Arel.sql("\"#{parent.table_name}\".\"id\"::varchar(255)")
    end

    ransacker :jsonb, args: [:parent, :ransacker_args] do |parent, attribute|
      if attribute =~ /_as_/
        attribute, type = attribute.split('_as_')
        segments = attribute.split('.')
        Arel::Nodes::NamedFunction.new(
          'cast', [
            Arel::Nodes::InfixOperation.new(
              '#>>',
              parent.table[segments.shift],
              Arel::Nodes.build_quoted(
                "{#{segments.join(',')}}",
              ),
            ).as(type),
          ],
        )
      else
        segments = attribute.split('.')
        Arel::Nodes::InfixOperation.new(
          '#>>',
          parent.table[segments.shift],
          Arel::Nodes.build_quoted(
            "{#{segments.join(',')}}",
          ),
        )
      end
    end
  end

  def ta_statistic(stat_condition)
    # 只使用只读的方式
    ActiveRecord::Base.while_preventing_writes do
      if stat_condition.is_a?(Hash)
        Com::Attr::Stat::Resource.new(stat_condition).calc(self)
      else
        stat_condition.calc(self)
      end
    end
  end

  # 通过属性过滤后更新对象，防止出现属性不存在导致的问题
  # 不支持association设置，只支持属性，给前端进行调用
  def ta_update(attributes: {})
    reflection_names = _reflections.values.map { |r| r.name.to_s }

    record_attributes =
      if self.class.respond_to?(:resource_permitted_params)
        attribute_params = attributes.is_a?(ActionController::Parameters) ? attributes : ActionController::Parameters.new(attributes)
        attribute_params.permit(self.class.resource_permitted_params)
      else
        attribute_params = attributes.respond_to?(:with_indifferent_access) ? attributes.with_indifferent_access : attributes
        attribute_params.except(:id, :created_at, :updated_at).delete_if { |k, _v| !respond_to?("#{k}=") || k.to_s.in?(reflection_names) }
      end

    update!(record_attributes)
  end

  def ta_assign_attributes(attributes: {})
    reflection_names = _reflections.values.map { |r| r.name.to_s }

    record_attributes =
      if self.class.respond_to?(:resource_permitted_params)
        attribute_params = attributes.is_a?(ActionController::Parameters) ? attributes : ActionController::Parameters.new(attributes)
        attribute_params.permit(self.class.resource_permitted_params)
      else
        attribute_params = attributes.respond_to?(:with_indifferent_access) ? attributes.with_indifferent_access : attributes
        attribute_params.except(:id, :created_at, :updated_at).delete_if { |k, _v| !respond_to?("#{k}=") || k.to_s.in?(reflection_names) }
      end
    assign_attributes(record_attributes)
  end

  def trigger_ta_update payload: , **opts
    ta_update(attributes: payload)
  end

  def error_text
    errors.full_messages.join("\n")
  end

  def class_name
    self.class.base_class.name
  end

  def try_chain(arr)
    arr.reduce(self) { |r, m| r.try(m) || r.try(:[], m) }
  end

  def send_chain(arr)
    arr.inject(self, :send)
  end

  def try_method(method)
    arr = method.to_s.split(/\./)
    try_chain arr
  end

  def send_method(method)
    arr = method.to_s.split(/\./)
    send_chain arr
  end

  def as_jbuilder_json(partial: 'detail', **options)
    obj = self
    partial_views = [
      obj.class.model_name.collection,
      obj.class.base_class.model_name.collection,
    ].uniq
    partial_view = partial_views.find { |_view| ApplicationController.new.lookup_context.any?("#{_view}/_#{partial}") }
    return nil if partial_view.blank?

    element_singular = partial_view.split('/').last.singularize
    view_context = ApplicationController.new
    options.each do |k, v|
      next unless k.to_s.start_with?('@')
      view_context.instance_variable_set(k.to_s, v)
    end
    JSON.parse view_context.render_to_string(
      partial: "#{partial_view}/#{partial}", locals: { element_singular.to_sym => obj }.merge(options.symbolize_keys),
    )
  rescue StandardError
    as_json
  end

  def as_form_json(forms, mode: 'simple')
    if forms.is_a?(String)
      template_forms = forms.split(',').map do |form_string|
        klass_singular, flag = form_string.split('#')
        ModelDefine.model_setting_by_singular_name(
          klass_singular,
          flag: flag,
          app: try(:app),
        )&.form
      end.compact
    else
      template_forms = Array.wrap(forms)
    end

    template_forms.reduce({}) do |h, form|
      h.merge! form.form_json(self, mode: mode)
    end
  end

  def enable_member_actions(controller_class:, user:, context: {}, policy_class: nil)
    routes = RailsCom::ActionRoute.routes controller_class
    controller = controller_class.new
    action_callbacks = controller._process_action_callbacks

    controller.send(:permit_perform, permit_auth: user) if action_callbacks.map(&:filter).include?(:permit_perform)

    policy_class ||= controller_class.instance_variable_get(:@policy_class)
    policy = policy_class&.new(user, self)

    routes.select! do |route|
      action = route.defaults[:action]
      is_member = route.required_parts&.include?(:id)
      next(false) unless is_member
      next(true) if policy_class.blank?

      query ||= "#{action}?"
      is_policy = policy_class.method_defined?(query) ?
        policy.public_send(query).present? : true

      is_member && is_policy
    end
    routes.map do |route|
      route.defaults[:action]
    end.uniq
  end

  class_methods do
    def after_initialize(&block)
      self.after_initialize_callbacks ||= []
      self.after_initialize_callbacks.push(block)
    end

    attr_accessor :track_migration

    # Returns whether this class is track migration or not
    def track_migration?
      defined?(@track_migration) && @track_migration == true
    end

    def extra_single_attributes
      try(:extra_view_attributes, :single)
    end

    def extra_simple_attributes
      try(:extra_view_attributes, :simple)
    end

    def extra_detail_attributes
      try(:extra_view_attributes, :detail)
    end

    def ransackable_scopes(_auth_object = nil)
      ransackable_scope_defines&.flatten
    end

    def ransackable_associations(auth_object = nil)
      @ransackable_associations ||= reflect_on_all_associations.map { |a| a.name.to_s }
    end

    def ransackable_attributes(auth_object = nil)
      @ransackable_attributes ||= column_names + _ransackers.keys + _ransack_aliases.keys + attribute_aliases.keys
    end

    def index(name, **options)
      h = { index: name, **options }
      self.indexes_to_define_after_schema_loads = indexes_to_define_after_schema_loads + [h]
    end

    def seqable(length: 4, prefix: nil)
      attribute :seq, :string, index: { unique: true }, comment: '编号'

      after_create :generate_seq_on_create
      define_method(:generate_seq_on_create) do
        # 如果创建时候已经有seq参数，则使用给定的，直接跳过
        return if seq.present?

        if respond_to?(:seq_generator)
          _seq = send(:seq_generator)
        else
          timestamp = Time.now.strftime('%Y%m%d')
          no = ("%0#{length}d" % id).last length
          prefix_ascii = try(:seq_prefix) || prefix || self.class.name.to_s.split('').map(&:ord).join[0..3]
          _seq = "#{prefix_ascii}#{timestamp}#{no}"
        end
        update_column :seq, _seq
      end
    end

    def uuidable(number: false)
      attribute :uuid, :string, index: { unique: true }, comment: '唯一标识UUID'

      default_value_for(:uuid) { SecureRandom.uuid }
    end

    def mergeable(*json_attributes)
      after_initialize do
        json_attributes.each do |json_attribute|
          class_eval do
            alias_method "replace_#{json_attribute}=", "#{json_attribute}="
            define_method("#{json_attribute}=") do |attribute_value|
              origin_value = (send(json_attribute) || {}).as_json.symbolize_keys
              merge_value = (attribute_value || {}).as_json.symbolize_keys
              value = origin_value.merge(merge_value)
              send "replace_#{json_attribute}=", value
            end
          end
        end
      end
    end

    def stiable(type: 'type')
      type = type.to_sym
      attribute type, :string, index: true, comment: 'STI属性'
      strip_attributes only: type
      self.inheritance_column = type.to_s

      # validates type, inclusion: {
      #   in: ->(object) { object.class.base_class.descendants.map(&:to_s).concat([nil, '', object.class.base_class.to_s]) },
      # }
    end

    def formable(flag: 'model')
      attribute "#{flag}_flag".to_sym, :string, comment: "#{flag} flag，对应model_setting的flag"
      attribute "#{flag}_payload".to_sym, :jsonb, comment: "#{flag} payload存储的字段"
      attribute "#{flag}_payload_summary".to_sym, :jsonb, comment: "#{flag} summary存储的字段"
      attribute "#{flag}_detail".to_sym, :jsonb, comment: "#{flag} 存储的详情字段"

      before_save "update_#{flag}_payload_summary".to_sym, if: "will_save_change_to_#{flag}_payload?".to_sym

      define_method("update_#{flag}_payload_summary".to_sym) do
        # 首先要拿到模型
        _forms_template = ::ModelDefine.model_setting_by_class(self.class, flag: flag, app: try(:app))&.forms_template
        _form = _forms_template&.form&.find_by_flag(flag)
        if _form.present? && _form.instance_of?(::Forms::Attr::Item)
          _payload_summary = _form.payload_value send("#{flag}_payload"), trans: true, summary: true
          send("#{flag}_payload_summary=", _payload_summary)
        end
      end
    end

    def payloadable(payload_attribute: 'payload', form_method: :form)
      attribute payload_attribute.to_sym, :jsonb, comment: "#{payload_attribute} payload存储的字段"
      attribute "#{payload_attribute}_summary".to_sym, :jsonb, comment: "#{payload_attribute} summary存储的字段"

      before_save "update_#{payload_attribute}_with_summary".to_sym, if: "will_save_change_to_#{payload_attribute}?".to_sym

      define_method("update_#{payload_attribute}_with_summary".to_sym) do
        # 首先要拿到模型
        _form = try(form_method)
        if _form.present? && _form.instance_of?(::Forms::Attr::Item)
          _payload_summary = _form.payload_value send(payload_attribute), trans: true, summary: true
          send("#{payload_attribute}_summary=", _payload_summary)
        end
      end
    end

    def effectable(effective_at: 'effective_at', invalid_at: 'invalid_at', field_type: 'datetime', association: nil, prefix: nil)
      effective_at_attribute = [prefix, effective_at].compact.join('_')
      invalid_at_attribute = [prefix, invalid_at].compact.join('_')
      effective_scope = [prefix, 'effective'].compact.join('_').to_sym
      invalid_scope = [prefix, 'invalid'].compact.join('_').to_sym
      pending_scope = [prefix, 'effect_pending'].compact.join('_').to_sym
      expire_scope = [prefix, 'effect_expire'].compact.join('_').to_sym
      will_expire_scope = [prefix, 'will_expire_in'].compact.join('_').to_sym # 将要到期
      not_expire_scope = [prefix, 'effect_not_expire'].compact.join('_').to_sym
      effective_method = [prefix, 'effective?'].compact.join('_').to_sym
      invalid_action_method = [prefix, 'invalid!'].compact.join('_').to_sym
      effective_state = [prefix, 'effective_state'].compact.join('_').to_sym

      # 使用 lambda 来替代方法定义
      parse_time_interval = ->(value, unit) {
        case unit.to_s.downcase
        when 'hour', 'hours', 'h'
          value.hours
        when 'day', 'days', 'd'
          value.days
        when 'week', 'weeks', 'w'
          value.weeks
        when 'month', 'months', 'm'
          value.months
        when 'year', 'years', 'y'
          value.years
        else
          raise ArgumentError, "Unsupported time unit: #{unit}. Supported units are: hours (h), days (d), weeks (w), months (m), years (y)"
        end
      }

      unless association
        case field_type.to_s
        when 'datetime'
          attribute effective_at_attribute.to_sym, :datetime, comment: '生效时间', index: true
          attribute invalid_at_attribute.to_sym, :datetime, comment: '失效时间', index: true
        when 'date'
          attribute effective_at_attribute.to_sym, :date, comment: '生效时间', index: true
          attribute invalid_at_attribute.to_sym, :date, comment: '失效时间', index: true
        end
      end

      association_ref = association.present? ?
        reflect_on_association(association) : nil
      _table_name = association_ref&.table_name || table_name

      # effective
      scope effective_scope, ->(time = Time.now) {
        _time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time
        _association = association_ref.present? ? all.joins(association.to_sym) : all

        _association.where("#{_table_name}.#{effective_at_attribute}" => nil).or(
          _association.where("#{_table_name}.#{effective_at_attribute} <= ?", _time),
        )
          .and(
            _association.where("#{_table_name}.#{invalid_at_attribute}" => nil).or(
              _association.where("#{_table_name}.#{invalid_at_attribute} >= ?", _time),
            ),
          )
      }

      # invalid
      scope invalid_scope, ->(time = Time.now) {
        _time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time
        _association = association_ref.present? ? all.joins(association.to_sym) : all
        _association.where("#{_table_name}.#{effective_at_attribute} > ?", _time).or(
          _association.where("#{_table_name}.#{invalid_at_attribute} < ?", _time),
        )
      }

      # effect_pending
      scope pending_scope, ->(time = Time.now) {
        _time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time
        _association = association_ref.present? ? all.joins(association.to_sym) : all
        _association.where("#{_table_name}.#{effective_at_attribute} > ?", _time)
      }

      # effect_not_expire
      scope not_expire_scope, ->(time = Time.now) {
        _time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time
        _association = association_ref.present? ? all.joins(association.to_sym) : all

        _association.where("#{_table_name}.#{invalid_at_attribute}" => nil).or(
          _association.where("#{_table_name}.#{invalid_at_attribute} >= ?", _time),
        )
      }

      # effect_expire
      scope expire_scope, ->(time = Time.now) {
        _time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time
        _association = association_ref.present? ? all.joins(association.to_sym) : all

        _association.where("#{_table_name}.#{invalid_at_attribute} < ?", _time)
      }

      scope will_expire_scope, ->(value, unit = 'days', time = Time.now) {
        # 计算时间间隔
        interval = parse_time_interval.call(value, unit)

        _start_time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time
        _end_time = field_type.to_s == 'date' ? (time.to_time + interval).to_date : (time.to_time + interval)

        _association = association_ref.present? ? all.joins(association.to_sym) : all

        # 查找在指定时间范围内会失效的记录
        _association.where("#{_table_name}.#{invalid_at_attribute} IS NOT NULL")
          .where("#{_table_name}.#{invalid_at_attribute} >= ?", _start_time)
          .where("#{_table_name}.#{invalid_at_attribute} <= ?", _end_time)
      }

      # 设置成datetime类型，但是前端需要date类型
      define_method("#{effective_at_attribute}_to_date") do
        try(effective_at_attribute)&.to_date
      end

      define_method("#{invalid_at_attribute}_to_date") do
        try(invalid_at_attribute)&.to_date
      end

      # effective?
      define_method(effective_method) do |time = Time.now|
        _time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time

        effective_at_value = association.present? ? try(association).try(effective_at_attribute) : try(effective_at_attribute)
        invalid_at_value = association.present? ? try(association).try(invalid_at_attribute) : try(invalid_at_attribute)

        (effective_at_value.blank? || effective_at_value <= _time) &&
          (invalid_at_value.blank? || invalid_at_value >= _time)
      end

      return if association

      # invalid!
      define_method(invalid_action_method) do
        if try(effective_method)
          _time = field_type.to_s == 'date' ? Time.zone.today : Time.zone.now

          update! invalid_at_attribute => _time
        end
      end

      # effective_state
      # pending processing completed unknow
      define_method(effective_state) do |time = Time.now|
        _time = field_type.to_s == 'date' ? time.to_time.to_date : time.to_time

        effective_at_value = association.present? ? try(association).try(effective_at_attribute) : try(effective_at_attribute)
        invalid_at_value = association.present? ? try(association).try(invalid_at_attribute) : try(invalid_at_attribute)

        if effective_at_value.present? && invalid_at_value.present?
          # 开始结束时间都存在
          if _time < effective_at_value
            'pending'
          elsif _time > invalid_at_value
            'completed'
          else
            'processing'
          end
        elsif effective_at_value.present? && invalid_at_value.blank?
          # 开始时间存在
          if _time < effective_at_value
            'pending'
          else
            'processing'
          end
        elsif effective_at_value.blank? && invalid_at_value.present?
          # 结束时间存在
          if _time > invalid_at_value
            'completed'
          else
            'processing'
          end
        else
          'unknow'
        end
      end
    end

    def treeable(**options)
      has_ancestry cache_depth: true, counter_cache: true, depth_cache_column: :depth, touch: true, **options

      attribute :depth, :integer, comment: '树结构深度'
      attribute :children_count, :integer, default: 0, comment: '子对象的数据'
      attribute :ancestry, :string, index: true, comment: '树形结构'

      scope :leaves, lambda {
        joins("LEFT JOIN #{table_name} AS c ON c.#{ancestry_column} = CAST(#{table_name}.id AS text) OR c.#{ancestry_column} = #{table_name}.#{ancestry_column} || '/' || #{table_name}.id").group("#{table_name}.id").having('COUNT(c.id) = 0')
      }

      ransackable_scope_defines.concat(
        %i[
        roots
        ancestors_of
        children_of
        subtree_of
        descendants_of
        siblings_of
        leaves
        ],
      ).uniq!
    end

    def closure_treeable(**options)
      include Com::Ext::ClosureTree

      has_closure_tree(**options)

      attribute :parent_id, :integer, comment: 'closure tree parent_id'

      hierarchy_class.track_migration = true
      hierarchy_class.attribute :ancestor_id, :integer, null: false
      hierarchy_class.attribute :descendant_id, :integer, null: false, index: { name: "#{name.underscore}_desc_idx" }
      hierarchy_class.attribute :generations, :integer, null: false
      hierarchy_class.attribute :created_at, :datetime, null: true
      hierarchy_class.attribute :updated_at, :datetime, null: true
      hierarchy_class.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "#{name.underscore}_anc_desc_idx"

      alias_method :subtree, :self_and_descendants
      alias_method :path, :self_and_ancestors

      ransackable_scope_defines.concat(
        %i[
        roots
        ancestors_of
        children_of
        subtree_of
        descendants_of
        siblings_of
        leaves
        ],
      ).uniq!
    end

    def has_paper_trail_version(options = {})
      defaults = {
        versions: { class_name: 'PaperTrailVersion' },
        version_relation_class_name: 'VersionRelationship',
        version_belongs: [],
      }
      options = defaults.merge(options)

      class_attribute :version_relation_class_name, :version_belongs

      self.version_relation_class_name = options.delete(:version_relation_class_name).presence
      self.version_belongs = options.delete(:version_belongs).unshift(:itself).uniq

      if version_relation_class_name
        has_many :version_relationships, class_name: version_relation_class_name, as: :resource
        has_many :real_version_relationships, class_name: version_relation_class_name, as: :real_resource
      end

      has_paper_trail(options)
    end

    def encryptable(encrypt_attribute)
      crypt_class = ENV['CRYPT'] == 'SM' ? Com::Crypt::Sm : Com::Crypt::Aes
      define_method(encrypt_attribute.to_sym) do
        crypt_class.easy_decrypt(super())
      end

      define_method("#{encrypt_attribute}=".to_sym) do |val|
        # iv = encrypt_iv.present? ? Digest::MD5.digest(encrypt_iv) : encrypt_iv
        encrypt_val = crypt_class.easy_encrypt(val)
        super(encrypt_val)
      end
    end

    # 自动引入engine对应module的model concern
    def auto_load_rails_engine_model_concern
      return unless name.present?

      engines = Rails::Engine.subclasses.select do |engine|
        engine.config.generators.api_only
      end.map(&:to_s).select { |cname| cname.start_with?('Rails') }
      engines_module = engines.map { |cname| cname.split('::')[0].sub(/^Rails/, '') }
      engines_module.each do |engine_module|
        base_class_name = name.demodulize
        base_module_name = name.deconstantize
        # # 如果是engine下的model，则只加载对应engine的model concern
        # next if base_module_name.present? && base_module_name != engine_module

        model_concern_name = engine_module == base_module_name ?
          "#{engine_module}::Model::#{base_class_name}" : "#{engine_module}::Model::#{name}"
        model_concern = model_concern_name.safe_constantize
        include model_concern if model_concern.present? && !included_modules.include?(model_concern)

        # 加入项目内代码，根目录下ext目录下对应的class name
        project_model_concern_name = "ModelExt::#{name}"
        project_model_concern = project_model_concern_name.safe_constantize
        include project_model_concern if project_model_concern.present? && !included_modules.include?(project_model_concern)
      end
    end

    def inherited(subclass)
      super

      subclass.auto_load_rails_engine_model_concern unless subclass.abstract_class?
    end
  end
end

ActiveSupport.on_load :active_record do
  include RailsCom::ActiveRecord::Include
end
