class String
  #  'model_payload.company.lat_as_float'
  #  "(model_payload#>>'{company,lat}')::float"
  def to_jsonb
    return self unless self =~ /\./

    if self =~ /_as_/
      attribute, attribute_type = split('_as_')
      segments = attribute.split('.')
    else
      attribute_type = nil
      segments = split('.')
    end

    jsonb_attribute = "#{segments.shift}#>>'{#{segments.join(',')}}'"
    jsonb_attribute = "(#{jsonb_attribute})::#{attribute_type}" if attribute_type.present?
    jsonb_attribute
  end
end
