class Hash
  def find_all_values_for(*keys)
    h = to_dot_hash
    h.with_indifferent_access.slice(*keys)
    #
    # result = []
    # result.concat values_at(*keys)
    # values.each do |hash_value|
    #   values = hash_value.is_a?(Array) ?
    #     hash_value : [hash_value]
    #   values.each do |value|
    #     result.concat value.find_all_values_for(*keys) if value.is_a? Hash
    #   end
    # end
    # result.compact
  end

  def to_dot_hash
    each_with_object({}) do |(key, value), squished|
      if value.is_a? Hash
        squished.store(key.to_s, value)
        value.to_dot_hash.each { |sub_key, sub_value| squished.store("#{key}.#{sub_key}", sub_value) }
      else
        squished.store(key.to_s, value)
      end
    end
  end

  def to_nested_hash
    each_with_object({}) do |(key, value), stretched|
      key_parts = key.to_s.split('.')

      if key_parts.size > 1
        level_key = key_parts.shift
        tail = key_parts.join('.')

        existing_content = stretched.fetch(level_key, {})
        new_content = existing_content.merge({ tail => value }).to_nested_hash

        stretched.store(level_key, new_content)
      else
        stretched.store(key.to_s, value)
      end
    end
  end

  def deconstruct_with_form_item form_item
    result = []
    form_item.each_field do |opts|
      value = Hash.run_form_item_chain(opts[:chain], self) do |v|
        result.push({
          field: opts[:field],
          is_bottom: (opts[:field].children || []).length == 0,
          value: v
        })
      end
    end
    result
  end

  def self.run_form_item_chain chain, value, &block
    _chain = [*chain]
    item = _chain.shift()
    if item
      Forms::Attr::Item.caculation(item, value) do |v|
        Hash.run_form_item_chain(_chain, v, &block)
      end
    else
      block.call(value)
    end
  end
end
