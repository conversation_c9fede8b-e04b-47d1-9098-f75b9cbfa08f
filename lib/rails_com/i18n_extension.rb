# frozen_string_literal: true

module RailsCom::I18nExtension
  # Notice::ArticlesController notice/articles
  def t_controller_name(controller_or_keypath)
    ttt(controller_or_keypath)
  end

  # Notice::Article  notice/article
  def t_model_name(model_or_keypath)
    ttt(model_or_keypath)
  end

  def t_platform_name(platform)
    ttt("platforms.#{platform}", platform)
  end

  def t_action_name(action, controller = nil)
    return _ttt("controllers.actions.#{action}", action) if controller.nil?

    klass = if controller.is_a?(Class)
              controller
            else
              controller.to_s.camelize.safe_constantize || "#{controller}_controller".camelize.safe_constantize
            end
    return t_action_name(action) if klass.nil?

    controller_path = _controller_path(klass)
    keypath = "controllers.#{controller_path}.actions.#{action}"
    _ttt(keypath, t_action_name(action))
  end

  def ttt(klass_or_keypath, fallback = nil)
    return if klass_or_keypath.nil?

    klass = klass_or_keypath.is_a?(Class) ? klass_or_keypath : "#{klass_or_keypath}".camelize.safe_constantize
    klass = "#{klass_or_keypath}_controller".camelize.safe_constantize if klass.nil?
    return _ttt(klass_or_keypath, fallback) if klass.nil?
    return klass.model_name.human if _is_ar_subclass?(klass)

    controller_path = _controller_path(klass_or_keypath)
    _ttt("controllers.#{controller_path}.name", controller_path) if _is_ac_subclass?(klass)
  end

  private

  def _ttt(key, fallback = nil)
    exists?(key) ? I18n.t(key) : fallback || key
  end

  def _is_ar_subclass?(klass)
    klass.ancestors.include?(ActiveRecord::Base)
  end

  def _is_ac_subclass?(klass)
    klass.ancestors.include?(ActionController::Metal)
  end

  def _controller_path(klass)
    klass.to_s.delete_suffix('Controller').underscore unless klass.nil?
  end
end
