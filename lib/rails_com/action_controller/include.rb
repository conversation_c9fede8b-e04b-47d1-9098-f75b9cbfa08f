module RailsCom::ActionController::Include
  extend ActiveSupport::Concern

  included do
    # 自动引入engine对应module的controller concern
    def auto_load_rails_engine_controller_concern
      return unless name.present?

      engines = Rails::Engine.subclasses.select do |engine|
        engine.config.generators.api_only
      end.map(&:to_s).select { |cname| cname.start_with?('Rails') }
      engines_module = engines.map { |cname| cname.split('::')[0].sub(/^Rails/, '') }
      engines_module.each do |engine_module|
        base_class_name = name.demodulize
        base_module_name = name.deconstantize
        # # 如果是engine下的controller，则只加载对应engine的controller concern
        # next if base_module_name.present? && base_module_name != engine_module

        controller_concern_name = engine_module == base_module_name ?
          "#{engine_module}::Controller::#{base_class_name}" : "#{engine_module}::Controller::#{name}"
        controller_concern = controller_concern_name.safe_constantize
        include controller_concern if controller_concern.present? && !included_modules.include?(controller_concern)

        # 加入项目内代码，根目录下ext目录下对应的class name
        project_controller_concern_name = "ControllerExt::#{name}"
        project_controller_concern = project_controller_concern_name.safe_constantize
        include project_controller_concern if project_controller_concern.present? && !included_modules.include?(project_controller_concern)
      end
    end
  end

  def inherited(subclass)
    super

    subclass.auto_load_rails_engine_controller_concern unless subclass.abstract_class?
  end
end

ActiveSupport.on_load :active_controller do
  include RailsCom::ActionController::Include
end
