module RailsCom::ActionRoute
  extend self

  def routes(controller)
    controller = controller.controller_path unless controller.is_a?(String)
    ::Rails.application.routes.routes.select do |route|
      route.defaults[:controller] == controller
    end
  end

  def route_actions(controller)
    routes(controller).map do |route|
      route.defaults[:action]
    end
  end
end

module RailsCom::ActionRoute
  module Exportable
    def exportable
      post :export, on: :collection
      post :export_async, on: :collection
      post :export_headers, on: :collection
    end
  end
end

module RailsCom::ActionRoute
  module Importable
    def importable
      post :upload_excel, on: :collection
      post :excel, on: :collection
      post :import, on: :collection
      post :import_async, on: :collection
      post :import_headers, on: :collection
      post :exchange, on: :collection
      post :export_import_template, on: :collection
    end
  end
end

module RailsCom::ActionRoute
  module Versionable

    def versionable
      get :version_relationships, on: :member, action: 'version_relationships_index'
      get 'version_relationships/:version_relationship_id', on: :member, action: 'version_relationships_show'

      get :paper_trail_versions, on: :member, action: 'paper_trail_versions_index'
      get 'paper_trail_versions/:version_id', on: :member, action: 'paper_trail_versions_show'
    end

  end
end

ActionDispatch::Routing::Mapper.include RailsCom::ActionRoute::Versionable
ActionDispatch::Routing::Mapper.include RailsCom::ActionRoute::Exportable
ActionDispatch::Routing::Mapper.include RailsCom::ActionRoute::Importable
# 默认增加post list_index
ActionDispatch::Routing::Mapper.class_eval do
  def resources(*resources, &block)
    options = resources.extract_options!.dup

    return self if apply_common_behavior_for(:resources, resources, options, &block)

    with_scope_level(:resources) do
      options = apply_action_options options
      resource_scope(ActionDispatch::Routing::Mapper::Resources::Resource.new(resources.pop, api_only?, @scope[:shallow], options)) do
        yield if block_given?

        concerns(options[:concerns]) if options[:concerns]

        collection do
          get :index if parent_resource.actions.include?(:index)
          post :list_index if parent_resource.actions.include?(:index)
          post :group_index if parent_resource.actions.include?(:index)
          post :create if parent_resource.actions.include?(:create)
        end

        if parent_resource.actions.include?(:new)
          new do
            get :new
          end
        end

        set_member_mappings_for_resource
      end
    end
  end

  self
end
