class RailsComMigration1619091119 < ActiveRecord::Migration[6.1]

  def change
    create_table :model_settings do |t|
      t.references :model_define
      t.references :setable, polymorphic: true
      t.references :app
      t.references :forms_template
      t.string :flag, comment: "同一个模型中的不同定义，其中model代表是这个对象的模型", default: "model"
      t.string :flag_name, comment: "flag对应中文名称", default: "模型定义"
      t.timestamps
    end
    create_table :apps do |t|
      t.string :code, comment: "应用标识"
      t.string :name, comment: "应用的名称"
      t.timestamps
    end
    create_table :roles do |t|
      t.references :resource, polymorphic: true
      t.string :name, index: true
      t.timestamps
    end
    create_table :model_defines do |t|
      t.string :klass, comment: "对应设置的Model名称"
      t.string :name, comment: "模型设置的中文名"
      t.string :association_chain, comment: "查找的关系列表", array: true
      t.string :klass_singular, comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
      t.timestamps
    end
    create_table :users_roles do |t|
      t.references :user
      t.references :role
      t.timestamps
    end
    create_table :users do |t|
      t.references :app
      t.string :account, comment: "账号，关联登录", index: true
      t.string :name, comment: "用户姓名"
      t.string :mobile, comment: "用户手机号"
      t.string :email, comment: "用户邮箱"
      t.timestamps
    end
    create_table :templates do |t|
      t.references :app
      t.string :uuid, comment: "表单的唯一标识，可以替代id给前端使用", index: true
      t.string :name, comment: "表单的名称"
      t.jsonb :form, default: {}
      t.timestamps
    end
  end
end
