class RailsComMigration1721049601 < ActiveRecord::Migration[7.1]

  def change
    create_table :async_tasks do |t|
      t.references :app
      t.references :user
      t.references :taskable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.string :type, comment: "STI属性"
      t.string :flag, comment: "程序使用参数，唯一标识，前端配合使用"
      t.string :name, comment: "任务名称"
      t.integer :progress, comment: "进度(取整数)"
      t.string :state
      t.string :perform_args, comment: "执行参数"
      t.jsonb :options, comment: "启动执行参数"
      t.jsonb :payload, comment: "处理信息"
      t.jsonb :result, comment: "异步处理的结果信息"
      t.jsonb :meta, comment: "额外信息"
      t.timestamps
    end
  end
end
