class RailsComMigration1697671379 < ActiveRecord::Migration[6.1]

  def change
    create_table :data_forms do |t|
      t.references :app
      t.references :create_user
      t.references :source, polymorphic: true
      t.references :record, polymorphic: true
      t.string :type, comment: "STI属性", index: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :source_flag, comment: "关联source的flag"
      t.string :state, comment: "数据状态"
      t.jsonb :payload, comment: "存储的信息"
      t.jsonb :summary, comment: "通过form生成的缩略信息"
      t.jsonb :form_conf
      t.jsonb :options, comment: "额外的数据信息"
      t.jsonb :meta, comment: "预留后续的数据存储"
      t.timestamps
    end
  end
end
