class RailsComMigration1707015729 < ActiveRecord::Migration[6.1]

  def change
    create_table :api_settings do |t|
      t.references :model_define
      t.references :app
      t.string :klass, comment: "对应的active record class name"
      t.string :action, comment: "对应controller的action"
      t.string :uid, comment: "自动生成的唯一标识", index: true
      t.jsonb :extract_conf
      t.timestamps
    end
  end
end
