class RailsComMigration1659996567 < ActiveRecord::Migration[6.1]

  def change
    create_table :component_settings do |t|
      t.references :app
      t.string :seq, comment: "编号", index: {:unique=>true}
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.string :name, comment: "组件配置名称"
      t.string :component_klass, comment: "组件类名称"
      t.string :component_path, comment: "组件类路径"
      t.jsonb :conf, comment: "组件配置的json结构"
      t.timestamps
    end
    create_table :page_settings do |t|
      t.references :app
      t.string :seq, comment: "编号", index: {:unique=>true}
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.string :name, comment: "页面配置名称"
      t.jsonb :conf, comment: "页面配置的json结构"
      t.timestamps
    end
  end
end
