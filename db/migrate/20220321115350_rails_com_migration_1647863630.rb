class RailsComMigration1647863630 < ActiveRecord::Migration[6.1]

  def change
    create_table :com_search_items do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.string :name, comment: "搜索条件 "
      t.integer :position, comment: "位置"
      t.string :group_name, comment: "分组标识"
      t.boolean :enabled, comment: "是否启用"
      t.jsonb :conditions
      t.timestamps
    end
  end
end
