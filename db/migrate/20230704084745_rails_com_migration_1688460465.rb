class RailsComMigration1688460465 < ActiveRecord::Migration[6.1]

  def change
    add_column :com_version_histories, :model_detail, :jsonb, comment: "model 存储的详情字段"
    add_column :users, :model_detail, :jsonb, comment: "model 存储的详情字段"
    add_column :component_settings, :model_detail, :jsonb, comment: "model 存储的详情字段"
    add_column :page_settings, :model_detail, :jsonb, comment: "model 存储的详情字段"
    add_column :com_search_items, :model_detail, :jsonb, comment: "model 存储的详情字段"
    add_column :com_themes, :model_detail, :jsonb, comment: "model 存储的详情字段"
    create_table :version_relationships do |t|
      t.references :app
      t.references :resource, polymorphic: true
      t.references :real_resource, polymorphic: true
      t.references :version, polymorphic: true
      t.references :operator, polymorphic: true
      t.timestamps
    end
    create_table :paper_trail_versions do |t|
      t.references :operator, polymorphic: true
      t.string :item_type, null: false
      t.integer :item_id, null: false
      t.string :event, comment: "create, update, destroy", null: false
      t.string :whodunnit, comment: "whodunnit"
      t.jsonb :object, comment: "object attributes"
      t.jsonb :object_changes, comment: "object changes"
      t.jsonb :controller_info, comment: "controller info"
      t.jsonb :model_info, comment: "model info"
      t.timestamps
      t.index [:item_type, :item_id], name: "index_versions_on_item_id_item_type"
    end
  end
end
