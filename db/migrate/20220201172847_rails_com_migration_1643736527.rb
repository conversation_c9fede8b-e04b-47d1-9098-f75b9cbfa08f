class RailsComMigration1643736527 < ActiveRecord::Migration[6.1]

  def change
    create_table :active_storage_blobs do |t|
      t.jsonb :metadata, comment: "额外信息"
      t.string :app_code, comment: "app标识"
      t.string :key, comment: "key"
      t.string :filename, comment: "文件名称"
      t.string :content_type, comment: "文件类型"
      t.string :service_name, comment: "服务名称"
      t.integer :byte_size, comment: "文件大小"
      t.string :checksum, comment: "校验位"
      t.timestamps
      t.index [:key], unique: true
    end
  end
end
